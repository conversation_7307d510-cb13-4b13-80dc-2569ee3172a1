"""
Main CLI entry point for ipsec-evaluator.

This module provides the main CLI application with global options and
coordinates all subcommands for configuration, testing, and analysis.
"""

import sys
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console

from ..utils.logging import setup_logging, get_logger
from .config import config_app
from .scenario import scenario_app
from .test import test_app
from .validate import validate_app

# Initialize main CLI app
app = typer.Typer(
    name="ipsec-evaluator",
    help="Enhanced IPsec compliance testing and evaluation tool",
    rich_markup_mode="rich",
    no_args_is_help=True
)

# Add subcommand groups
app.add_typer(config_app, name="config", help="Configuration management commands")
app.add_typer(scenario_app, name="scenario", help="Test scenario management commands")
app.add_typer(test_app, name="test", help="Test execution commands")
app.add_typer(validate_app, name="validate", help="Compliance validation commands")

console = Console()
logger = get_logger(__name__)


@app.callback()
def main(
    ctx: typer.Context,
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose output"),
    log_level: str = typer.Option("INFO", "--log-level", help="Set logging level"),
    config_file: Optional[Path] = typer.Option(None, "--config", "-c", help="Global configuration file path"),
    no_color: bool = typer.Option(False, "--no-color", help="Disable colored output"),
    quiet: bool = typer.Option(False, "--quiet", "-q", help="Suppress non-essential output")
):
    """
    Enhanced IPsec compliance testing and evaluation tool.

    This tool provides comprehensive IPsec protocol testing with support for:
    - ANSSI compliance validation
    - Multiple cryptographic algorithms (secp, brainpool curves)
    - Automated test scenario execution
    - Rich reporting and analysis
    - Configuration management
    """
    # Setup logging
    setup_logging(verbose=verbose, level=log_level)

    # Configure console
    if no_color:
        console._color_system = None

    # Store global options in context for subcommands
    ctx.ensure_object(dict)
    ctx.obj.update({
        'verbose': verbose,
        'log_level': log_level,
        'config_file': config_file,
        'no_color': no_color,
        'quiet': quiet,
        'console': console
    })

    # Log startup information
    if verbose:
        logger.debug(f"ipsec-evaluator CLI started with options: verbose={verbose}, log_level={log_level}")
        if config_file:
            logger.debug(f"Global configuration file: {config_file}")


@app.command("version")
def version():
    """Show version information."""
    try:
        from .. import __version__
        version_str = __version__
    except ImportError:
        version_str = "development"

    console.print(f"[bold blue]ipsec-evaluator[/bold blue] version [green]{version_str}[/green]")
    console.print("Enhanced IPsec compliance testing and evaluation tool")
    console.print("Based on ipsecdr with modern Python architecture")


@app.command("info")
def info():
    """Show system and environment information."""
    import platform
    import sys
    from pathlib import Path

    console.print("[bold blue]System Information[/bold blue]")

    # System info
    info_table = [
        ("Python Version", f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"),
        ("Platform", platform.platform()),
        ("Architecture", platform.machine()),
        ("Python Executable", sys.executable),
        ("Working Directory", str(Path.cwd())),
    ]

    for label, value in info_table:
        console.print(f"[cyan]{label}:[/cyan] {value}")

    # Check dependencies
    console.print("\n[bold blue]Key Dependencies[/bold blue]")

    dependencies = [
        ("cryptography", "Cryptographic operations"),
        ("pydantic", "Configuration validation"),
        ("rich", "Rich terminal output"),
        ("typer", "CLI framework"),
        ("scapy", "Packet manipulation"),
    ]

    for dep_name, description in dependencies:
        try:
            module = __import__(dep_name)
            version = getattr(module, '__version__', 'unknown')
            console.print(f"[green]✓[/green] [cyan]{dep_name}[/cyan] {version} - {description}")
        except ImportError:
            console.print(f"[red]✗[/red] [cyan]{dep_name}[/cyan] - {description} (not installed)")


@app.command("doctor")
def doctor():
    """Run system diagnostics and check configuration."""
    console.print("[bold blue]🔍 Running System Diagnostics[/bold blue]")

    checks = []

    # Check Python version
    if sys.version_info >= (3, 12):
        checks.append(("Python Version", True, f"Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"))
    else:
        checks.append(("Python Version", False, f"Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} (3.12+ required)"))

    # Check critical dependencies
    critical_deps = ['cryptography', 'pydantic', 'rich', 'typer']
    for dep in critical_deps:
        try:
            __import__(dep)
            checks.append((f"{dep} module", True, "Available"))
        except ImportError:
            checks.append((f"{dep} module", False, "Missing"))

    # Check configuration system
    try:
        from ..utils.configuration import IPsecEvaluatorConfigParser
        parser = IPsecEvaluatorConfigParser.from_template('basic')
        checks.append(("Configuration System", True, "Working"))
    except Exception as e:
        checks.append(("Configuration System", False, f"Error: {e}"))

    # Check crypto system
    try:
        from ..core.crypto.dh import EnhancedDHManager
        dh_manager = EnhancedDHManager()
        group_count = len(dh_manager.get_supported_groups())
        checks.append(("Crypto System", True, f"{group_count} DH groups available"))
    except Exception as e:
        checks.append(("Crypto System", False, f"Error: {e}"))

    # Check test framework
    try:
        from ..engine.tester import EnhancedTester
        from ..engine.checker import EnhancedChecker
        checks.append(("Test Framework", True, "Available"))
    except Exception as e:
        checks.append(("Test Framework", False, f"Error: {e}"))

    # Display results
    console.print()
    all_passed = True
    for check_name, passed, details in checks:
        status = "[green]✓[/green]" if passed else "[red]✗[/red]"
        console.print(f"{status} [cyan]{check_name}:[/cyan] {details}")
        if not passed:
            all_passed = False

    console.print()
    if all_passed:
        console.print("[green]🎉 All checks passed! System is ready for IPsec testing.[/green]")
    else:
        console.print("[red]⚠️  Some checks failed. Please resolve issues before running tests.[/red]")
        sys.exit(1)


def cli_main():
    """Main entry point for the CLI application."""
    try:
        app()
    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user.[/yellow]")
        sys.exit(130)
    except Exception as e:
        console.print(f"\n[red]Unexpected error:[/red] {e}")
        if "--verbose" in sys.argv or "-v" in sys.argv:
            import traceback
            console.print(f"[red]Traceback:[/red]\n{traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    cli_main()