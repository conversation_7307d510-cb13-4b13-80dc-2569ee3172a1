"""
Test scenario management CLI commands.

This module provides commands for managing test scenarios including:
- Listing available scenarios
- Creating new scenarios from templates
- Validating scenario definitions
- Scenario execution planning
- Batch scenario operations
"""

from pathlib import Path
from typing import Optional, List
import json

import typer
from rich.console import Console
from rich.table import Table
from rich.tree import Tree
from rich.panel import Panel
from rich.prompt import Confirm, Prompt
import yaml

from ..utils.logging import get_logger

# Scenario subcommand group
scenario_app = typer.Typer(
    name="scenario",
    help="Test scenario management commands",
    rich_markup_mode="rich"
)

console = Console()
logger = get_logger(__name__)


@scenario_app.command("list")
def scenario_list(
    scenarios_dir: Optional[Path] = typer.Option(
        None, "--dir", "-d", help="Scenarios directory path"
    ),
    filter_tag: Optional[str] = typer.Option(
        None, "--tag", "-t", help="Filter scenarios by tag"
    ),
    filter_type: Optional[str] = typer.Option(
        None, "--type", help="Filter scenarios by type (basic, anssi, performance)"
    ),
    show_details: bool = typer.Option(
        False, "--details", help="Show detailed scenario information"
    )
):
    """List available test scenarios."""
    
    if scenarios_dir is None:
        scenarios_dir = Path("scenarios")
    
    console.print(f"[bold blue]Available Test Scenarios[/bold blue]")
    console.print(f"Directory: [dim]{scenarios_dir}[/dim]")
    
    if not scenarios_dir.exists():
        console.print(f"[red]Error:[/red] Directory not found: {scenarios_dir}")
        console.print("[yellow]Tip:[/yellow] Create scenarios with 'ipsec-evaluator scenario create'")
        raise typer.Exit(1)
    
    # Find scenario files
    scenario_files = []
    for pattern in ["*.yml", "*.yaml", "*.json"]:
        scenario_files.extend(scenarios_dir.glob(pattern))
    
    if not scenario_files:
        console.print("[yellow]No scenario files found[/yellow]")
        console.print("[yellow]Tip:[/yellow] Create scenarios with 'ipsec-evaluator scenario create'")
        return
    
    if show_details:
        _show_detailed_scenarios(scenario_files, filter_tag, filter_type)
    else:
        _show_scenario_table(scenario_files, filter_tag, filter_type)


@scenario_app.command("create")
def scenario_create(
    name: str = typer.Argument(..., help="Scenario name"),
    template: str = typer.Option("basic", "--template", "-t", help="Scenario template type"),
    output_dir: Optional[Path] = typer.Option(None, "--output", "-o", help="Output directory"),
    format_type: str = typer.Option("yaml", "--format", "-f", help="Output format (yaml, json)"),
    interactive: bool = typer.Option(False, "--interactive", "-i", help="Interactive scenario creation")
):
    """Create a new test scenario from template."""
    
    if output_dir is None:
        output_dir = Path("scenarios")
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Determine output file
    extension = "yml" if format_type.lower() in ["yaml", "yml"] else "json"
    output_file = output_dir / f"{name}.{extension}"
    
    if output_file.exists():
        if not Confirm.ask(f"Scenario file [bold]{output_file}[/bold] already exists. Overwrite?"):
            console.print("[yellow]Scenario creation cancelled.[/yellow]")
            raise typer.Exit(1)
    
    try:
        # Create scenario from template
        scenario_data = _create_scenario_template(template, name)
        
        # Interactive mode
        if interactive:
            scenario_data = _interactive_scenario_setup(scenario_data)
        
        # Save scenario
        if format_type.lower() in ["yaml", "yml"]:
            with open(output_file, 'w') as f:
                yaml.dump(scenario_data, f, default_flow_style=False)
        else:
            with open(output_file, 'w') as f:
                json.dump(scenario_data, f, indent=2)
        
        console.print(f"[green]✓[/green] Scenario created: [bold]{output_file}[/bold]")
        console.print(f"[blue]Template:[/blue] {template}")
        console.print(f"[blue]Format:[/blue] {format_type}")
        
        # Show next steps
        console.print("\n[bold]Next steps:[/bold]")
        console.print(f"1. Review and customize [bold]{output_file}[/bold]")
        console.print(f"2. Validate scenario: [bold]ipsec-evaluator scenario validate {output_file}[/bold]")
        console.print(f"3. Run scenario: [bold]ipsec-evaluator test run {name}[/bold]")
        
    except Exception as e:
        console.print(f"[red]Error creating scenario:[/red] {e}")
        raise typer.Exit(1)


@scenario_app.command("validate")
def scenario_validate(
    scenario_file: Path = typer.Argument(..., help="Scenario file to validate"),
    show_details: bool = typer.Option(False, "--details", "-d", help="Show detailed validation results")
):
    """Validate a scenario file."""
    
    if not scenario_file.exists():
        console.print(f"[red]Error:[/red] Scenario file not found: {scenario_file}")
        raise typer.Exit(1)
    
    try:
        # Load and validate scenario
        scenario_data = _load_scenario_file(scenario_file)
        
        # Basic validation
        validation_errors = _validate_scenario_data(scenario_data)
        
        if validation_errors:
            console.print(f"[red]✗[/red] Scenario validation failed:")
            for error in validation_errors:
                console.print(f"  [red]•[/red] {error}")
            raise typer.Exit(1)
        else:
            console.print(f"[green]✓[/green] Scenario file [bold]{scenario_file}[/bold] is valid")
            
            if show_details:
                _show_scenario_details(scenario_data)
        
    except Exception as e:
        console.print(f"[red]Error:[/red] {e}")
        raise typer.Exit(1)


@scenario_app.command("templates")
def scenario_templates():
    """List available scenario templates."""
    
    templates = {
        'basic': 'Basic IPsec test scenario with standard exchanges',
        'anssi': 'ANSSI-compliant scenario with French security requirements',
        'performance': 'Performance testing scenario with multiple algorithms',
        'compliance': 'Comprehensive compliance testing scenario',
        'custom': 'Custom scenario template for advanced users'
    }
    
    table = Table(title="Available Scenario Templates")
    table.add_column("Template", style="cyan", no_wrap=True)
    table.add_column("Description", style="white")
    
    for template_name, description in templates.items():
        table.add_row(template_name, description)
    
    console.print(table)
    
    console.print("\n[bold]Usage:[/bold]")
    console.print("ipsec-evaluator scenario create [scenario_name] --template [template_name]")


def _show_scenario_table(scenario_files: List[Path], filter_tag: Optional[str], filter_type: Optional[str]):
    """Show scenarios in table format."""
    table = Table(title="Test Scenarios", show_header=True, header_style="bold magenta")
    table.add_column("Name", style="cyan", no_wrap=True)
    table.add_column("File", style="blue")
    table.add_column("Type", style="green")
    table.add_column("Tags", style="yellow")
    table.add_column("Status", style="white")
    
    for file_path in sorted(scenario_files):
        try:
            scenario_data = _load_scenario_file(file_path)
            
            name = scenario_data.get('name', file_path.stem)
            scenario_type = scenario_data.get('type', 'basic')
            tags = ', '.join(scenario_data.get('tags', ['basic']))
            status = 'ready' if _validate_scenario_data(scenario_data) == [] else 'invalid'
            
            # Apply filters
            if filter_tag and filter_tag not in scenario_data.get('tags', []):
                continue
            if filter_type and filter_type != scenario_type:
                continue
            
            table.add_row(name, file_path.name, scenario_type, tags, status)
            
        except Exception as e:
            table.add_row(
                file_path.stem, 
                file_path.name, 
                "[red]error[/red]", 
                "", 
                f"[red]Error: {str(e)[:30]}...[/red]"
            )
    
    console.print(table)


def _show_detailed_scenarios(scenario_files: List[Path], filter_tag: Optional[str], filter_type: Optional[str]):
    """Show detailed scenario information."""
    for file_path in sorted(scenario_files):
        try:
            scenario_data = _load_scenario_file(file_path)
            
            # Apply filters
            if filter_tag and filter_tag not in scenario_data.get('tags', []):
                continue
            if filter_type and filter_type != scenario_data.get('type', 'basic'):
                continue
            
            _show_scenario_details(scenario_data, file_path.name)
            console.print()
            
        except Exception as e:
            console.print(f"[red]Error loading {file_path.name}:[/red] {e}")


def _show_scenario_details(scenario_data: dict, filename: Optional[str] = None):
    """Show detailed information about a scenario."""
    title = f"Scenario: {scenario_data.get('name', 'Unknown')}"
    if filename:
        title += f" ({filename})"
    
    details = []
    details.append(f"[cyan]Type:[/cyan] {scenario_data.get('type', 'basic')}")
    details.append(f"[cyan]Description:[/cyan] {scenario_data.get('description', 'No description')}")
    details.append(f"[cyan]Tags:[/cyan] {', '.join(scenario_data.get('tags', ['basic']))}")
    
    if 'exchanges' in scenario_data:
        details.append(f"[cyan]Exchanges:[/cyan] {len(scenario_data['exchanges'])}")
    
    if 'crypto_config' in scenario_data:
        crypto = scenario_data['crypto_config']
        details.append(f"[cyan]DH Groups:[/cyan] {', '.join(map(str, crypto.get('dh_groups', [])))}")
    
    panel_content = '\n'.join(details)
    console.print(Panel(panel_content, title=title, border_style="blue"))


def _load_scenario_file(file_path: Path) -> dict:
    """Load scenario data from file."""
    with open(file_path, 'r') as f:
        if file_path.suffix.lower() in ['.yml', '.yaml']:
            return yaml.safe_load(f) or {}
        else:
            return json.load(f)


def _validate_scenario_data(scenario_data: dict) -> List[str]:
    """Validate scenario data and return list of errors."""
    errors = []
    
    # Required fields
    if 'name' not in scenario_data:
        errors.append("Missing required field: 'name'")
    
    if 'type' not in scenario_data:
        errors.append("Missing required field: 'type'")
    
    # Validate type
    valid_types = ['basic', 'anssi', 'performance', 'compliance', 'custom']
    if scenario_data.get('type') not in valid_types:
        errors.append(f"Invalid type. Must be one of: {', '.join(valid_types)}")
    
    # Validate exchanges if present
    if 'exchanges' in scenario_data:
        exchanges = scenario_data['exchanges']
        if not isinstance(exchanges, list):
            errors.append("'exchanges' must be a list")
        elif len(exchanges) == 0:
            errors.append("'exchanges' cannot be empty")
    
    return errors


def _create_scenario_template(template: str, name: str) -> dict:
    """Create scenario data from template."""
    base_scenario = {
        'name': name,
        'description': f'Test scenario: {name}',
        'type': template,
        'tags': [template],
        'version': '1.0'
    }
    
    if template == 'basic':
        base_scenario.update({
            'exchanges': ['IKE_SA_INIT', 'IKE_AUTH'],
            'crypto_config': {
                'dh_groups': [19],
                'encryption': ['AES_GCM_16'],
                'integrity': ['HMAC_SHA2_256']
            }
        })
    elif template == 'anssi':
        base_scenario.update({
            'exchanges': ['IKE_SA_INIT', 'IKE_AUTH', 'CREATE_CHILD_SA'],
            'crypto_config': {
                'dh_groups': [19, 28, 20, 29],  # secp and brainpool
                'encryption': ['AES_GCM_16', 'AES_CTR'],
                'integrity': ['HMAC_SHA2_256', 'HMAC_SHA2_384']
            },
            'compliance': {
                'standard': 'anssi',
                'level': 'enhanced'
            }
        })
    elif template == 'performance':
        base_scenario.update({
            'exchanges': ['IKE_SA_INIT', 'IKE_AUTH'],
            'crypto_config': {
                'dh_groups': [19, 20, 21, 31],
                'encryption': ['AES_GCM_16', 'AES_CTR', 'CHACHA20_POLY1305'],
                'integrity': ['HMAC_SHA2_256']
            },
            'performance': {
                'iterations': 100,
                'concurrent': 4
            }
        })
    
    return base_scenario


def _interactive_scenario_setup(scenario_data: dict) -> dict:
    """Interactive scenario setup."""
    console.print("[bold]Interactive Scenario Setup[/bold]")
    console.print("Press Enter to keep default values, or enter new values.\n")
    
    # Basic information
    scenario_data['description'] = Prompt.ask(
        "Scenario description",
        default=scenario_data.get('description', '')
    )
    
    # Tags
    current_tags = ', '.join(scenario_data.get('tags', []))
    new_tags = Prompt.ask(
        "Tags (comma-separated)",
        default=current_tags
    )
    scenario_data['tags'] = [tag.strip() for tag in new_tags.split(',') if tag.strip()]
    
    return scenario_data
