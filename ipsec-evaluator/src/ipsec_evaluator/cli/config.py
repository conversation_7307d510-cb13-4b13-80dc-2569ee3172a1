
"""
Configuration management CLI commands.

This module provides comprehensive configuration management commands including:
- Configuration file initialization from templates
- Configuration validation and verification
- Configuration format conversion
- Interactive configuration setup
- Configuration display and inspection
"""

from pathlib import Path
from typing import Optional, Dict, Any

import typer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.prompt import Confirm, Prompt
import toml
import yaml

from ..utils.configuration import IPsecEvaluatorConfigParser, ConfigurationError
from ..utils.logging import get_logger

# Configuration subcommand group
config_app = typer.Typer(
    name="config",
    help="Configuration management commands",
    rich_markup_mode="rich"
)

console = Console()
logger = get_logger(__name__)


@config_app.command("init")
def config_init(
    template: str = typer.Option("basic", "--template", "-t", help="Configuration template type"),
    output: Path = typer.Option("ipsec-evaluator.toml", "--output", "-o", help="Output configuration file"),
    format_type: str = typer.Option("toml", "--format", "-f", help="Output format (toml, yaml)"),
    force: bool = typer.Option(False, "--force", help="Overwrite existing file"),
    interactive: bool = typer.Option(False, "--interactive", "-i", help="Interactive configuration")
):
    """Initialize a new configuration file from template."""

    # Check if output file exists
    if output.exists() and not force:
        if not Confirm.ask(f"Configuration file [bold]{output}[/bold] already exists. Overwrite?"):
            console.print("[yellow]Configuration initialization cancelled.[/yellow]")
            raise typer.Exit(1)

    try:
        # Get available templates
        available_templates = ['basic', 'anssi', 'performance', 'development']

        if template not in available_templates:
            console.print(f"[red]Error:[/red] Unknown template '{template}'")
            console.print(f"Available templates: {', '.join(available_templates)}")
            raise typer.Exit(1)

        # Create configuration from template
        template_data = IPsecEvaluatorConfigParser.create_template(template)

        # Interactive mode
        if interactive:
            template_data = _interactive_config_setup(template_data)

        # Save configuration
        if format_type.lower() == 'toml':
            with open(output, 'w') as f:
                toml.dump(template_data, f)
        elif format_type.lower() in {'yaml', 'yml'}:
            with open(output, 'w') as f:
                yaml.dump(template_data, f, default_flow_style=False)
        else:
            console.print(f"[red]Error:[/red] Unsupported format '{format_type}'")
            raise typer.Exit(1)

        console.print(f"[green]✓[/green] Configuration file created: [bold]{output}[/bold]")
        console.print(f"[blue]Template:[/blue] {template}")
        console.print(f"[blue]Format:[/blue] {format_type}")

        # Show next steps
        console.print("\n[bold]Next steps:[/bold]")
        console.print(f"1. Review and customize [bold]{output}[/bold]")
        console.print(f"2. Validate configuration: [bold]ipsec-evaluator config validate {output}[/bold]")
        console.print(f"3. Run tests: [bold]ipsec-evaluator test run --config {output}[/bold]")

    except Exception as e:
        console.print(f"[red]Error creating configuration:[/red] {e}")
        raise typer.Exit(1)


@config_app.command("validate")
def config_validate(
    config_file: Path = typer.Argument(..., help="Configuration file to validate"),
    show_details: bool = typer.Option(False, "--details", "-d", help="Show detailed validation results")
):
    """Validate a configuration file."""

    if not config_file.exists():
        console.print(f"[red]Error:[/red] Configuration file not found: {config_file}")
        raise typer.Exit(1)

    try:
        # Load and validate configuration
        parser = IPsecEvaluatorConfigParser(
            config_files=[config_file],
            validate_config=True
        )

        console.print(f"[green]✓[/green] Configuration file [bold]{config_file}[/bold] is valid")

        if show_details:
            _show_config_details(parser)

    except ConfigurationError as e:
        console.print(f"[red]✗[/red] Configuration validation failed:")
        console.print(f"[red]{e}[/red]")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]Error:[/red] {e}")
        raise typer.Exit(1)


@config_app.command("show")
def config_show(
    config_file: Path = typer.Argument(..., help="Configuration file to display"),
    section: Optional[str] = typer.Option(None, "--section", "-s", help="Show specific section only"),
    format_output: str = typer.Option("table", "--format", "-f", help="Output format (table, json, yaml)")
):
    """Display configuration file contents."""

    if not config_file.exists():
        console.print(f"[red]Error:[/red] Configuration file not found: {config_file}")
        raise typer.Exit(1)

    try:
        parser = IPsecEvaluatorConfigParser(config_files=[config_file])

        if format_output == "table":
            _show_config_table(parser, section)
        elif format_output == "json":
            import json
            config_dict = parser.get_config_summary()
            console.print(json.dumps(config_dict, indent=2))
        elif format_output == "yaml":
            config_dict = parser.get_config_summary()
            console.print(yaml.dump(config_dict, default_flow_style=False))
        else:
            console.print(f"[red]Error:[/red] Unsupported format '{format_output}'")
            raise typer.Exit(1)

    except Exception as e:
        console.print(f"[red]Error:[/red] {e}")
        raise typer.Exit(1)


@config_app.command("templates")
def config_templates():
    """List available configuration templates."""

    templates = {
        'basic': 'Basic IPsec testing configuration',
        'anssi': 'ANSSI-compliant configuration with French security requirements',
        'performance': 'High-performance testing configuration with multiple algorithms',
        'development': 'Development-friendly configuration with local settings'
    }

    table = Table(title="Available Configuration Templates")
    table.add_column("Template", style="cyan", no_wrap=True)
    table.add_column("Description", style="white")

    for template_name, description in templates.items():
        table.add_row(template_name, description)

    console.print(table)

    console.print("\n[bold]Usage:[/bold]")
    console.print("ipsec-evaluator config init --template [template_name]")


@config_app.command("convert")
def config_convert(
    input_file: Path = typer.Argument(..., help="Input configuration file"),
    output_file: Path = typer.Argument(..., help="Output configuration file"),
    input_format: Optional[str] = typer.Option(None, "--input-format", help="Input format (auto-detect if not specified)"),
    output_format: Optional[str] = typer.Option(None, "--output-format", help="Output format (auto-detect if not specified)"),
    force: bool = typer.Option(False, "--force", help="Overwrite existing output file")
):
    """Convert configuration file between formats."""

    if not input_file.exists():
        console.print(f"[red]Error:[/red] Input file not found: {input_file}")
        raise typer.Exit(1)

    if output_file.exists() and not force:
        if not Confirm.ask(f"Output file [bold]{output_file}[/bold] already exists. Overwrite?"):
            console.print("[yellow]Conversion cancelled.[/yellow]")
            raise typer.Exit(1)

    try:
        # Load configuration
        parser = IPsecEvaluatorConfigParser(config_files=[input_file])

        # Determine output format
        if output_format is None:
            output_format = output_file.suffix.lstrip('.')

        # Save in new format
        parser.save_config(output_file, output_format)

        console.print(f"[green]✓[/green] Configuration converted:")
        console.print(f"[blue]From:[/blue] {input_file} ({input_file.suffix})")
        console.print(f"[blue]To:[/blue] {output_file} ({output_format})")

    except Exception as e:
        console.print(f"[red]Error:[/red] {e}")
        raise typer.Exit(1)


def _interactive_config_setup(template_data: Dict[str, Any]) -> Dict[str, Any]:
    """Interactive configuration setup."""
    console.print("[bold]Interactive Configuration Setup[/bold]")
    console.print("Press Enter to keep default values, or enter new values.\n")

    # Network configuration
    console.print("[bold blue]Network Configuration[/bold blue]")
    network = template_data.get('network', {})

    network['initiator_ip'] = Prompt.ask(
        "Initiator IP address",
        default=network.get('initiator_ip', '************')
    )

    network['responder_ip'] = Prompt.ask(
        "Responder IP address",
        default=network.get('responder_ip', '************')
    )

    network['ike_port'] = int(Prompt.ask(
        "IKE port",
        default=str(network.get('ike_port', 500))
    ))

    # Global configuration
    console.print("\n[bold blue]Global Configuration[/bold blue]")
    global_config = template_data.get('global_config', {})

    global_config['max_concurrent_tests'] = int(Prompt.ask(
        "Maximum concurrent tests",
        default=str(global_config.get('max_concurrent_tests', 2))
    ))

    global_config['test_timeout'] = int(Prompt.ask(
        "Test timeout (seconds)",
        default=str(global_config.get('test_timeout', 300))
    ))

    global_config['results_dir'] = Prompt.ask(
        "Results directory",
        default=global_config.get('results_dir', './test_results')
    )

    template_data['network'] = network
    template_data['global_config'] = global_config

    return template_data


def _show_config_details(parser: IPsecEvaluatorConfigParser):
    """Show detailed configuration information."""
    summary = parser.get_config_summary()

    # Global configuration
    global_panel = Panel(
        _format_config_section(summary['global_config']),
        title="[bold]Global Configuration[/bold]",
        border_style="blue"
    )
    console.print(global_panel)

    # Network configuration
    network_panel = Panel(
        _format_config_section(summary['network']),
        title="[bold]Network Configuration[/bold]",
        border_style="green"
    )
    console.print(network_panel)

    # Crypto configuration
    crypto_panel = Panel(
        _format_config_section(summary['crypto']),
        title="[bold]Crypto Configuration[/bold]",
        border_style="yellow"
    )
    console.print(crypto_panel)

    # PKI configuration (if present)
    if summary['pki']:
        pki_panel = Panel(
            _format_config_section(summary['pki']),
            title="[bold]PKI Configuration[/bold]",
            border_style="red"
        )
        console.print(pki_panel)


def _format_config_section(section_data: Dict[str, Any]) -> str:
    """Format a configuration section for display."""
    lines = []
    for key, value in section_data.items():
        if isinstance(value, list):
            value_str = ', '.join(str(v) for v in value)
        else:
            value_str = str(value)
        lines.append(f"[cyan]{key}:[/cyan] {value_str}")
    return '\n'.join(lines)


def _show_config_table(parser: IPsecEvaluatorConfigParser, section: Optional[str] = None):
    """Show configuration in table format."""
    summary = parser.get_config_summary()

    if section:
        if section in summary:
            table = Table(title=f"{section.title()} Configuration")
            table.add_column("Setting", style="cyan")
            table.add_column("Value", style="white")

            section_data = summary[section]
            for key, value in section_data.items():
                if isinstance(value, list):
                    value_str = ', '.join(str(v) for v in value)
                else:
                    value_str = str(value)
                table.add_row(key, value_str)

            console.print(table)
        else:
            console.print(f"[red]Error:[/red] Unknown section '{section}'")
            console.print(f"Available sections: {', '.join(summary.keys())}")
    else:
        # Show all sections
        for section_name, section_data in summary.items():
            if section_name in ['config_files', 'validation_enabled', 'env_prefix']:
                continue  # Skip metadata

            if section_data:  # Only show non-empty sections
                table = Table(title=f"{section_name.replace('_', ' ').title()}")
                table.add_column("Setting", style="cyan")
                table.add_column("Value", style="white")

                for key, value in section_data.items():
                    if isinstance(value, list):
                        value_str = ', '.join(str(v) for v in value)
                    else:
                        value_str = str(value)
                    table.add_row(key, value_str)

                console.print(table)
                console.print()
