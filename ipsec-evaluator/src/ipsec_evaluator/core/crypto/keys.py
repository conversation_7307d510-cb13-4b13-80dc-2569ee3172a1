"""
Key management and derivation for IPsec.

This module handles key generation, derivation, and management
for IKEv2 and ESP operations.
"""

import secrets
from typing import Dict

from cryptography.hazmat.primitives import hashes, hmac
from cryptography.hazmat.primitives.kdf.hkdf import HKDF
from cryptography.hazmat.backends import default_backend


from .algorithms import PRFAlgorithm


class KeyManager:
    """Manages cryptographic keys and key derivation."""

    def __init__(self):
        super().__init__()
        self._backend = default_backend()

    def generate_random_key(self, length: int) -> bytes:
        """Generate a random key of specified length."""
        return secrets.token_bytes(length)

    def derive_ikev2_keys(
        self,
        prf: PRFAlgorithm,
        shared_secret: bytes,
        nonce_i: bytes,
        nonce_r: bytes,
        spi_i: bytes,
        spi_r: bytes,
        key_lengths: Dict[str, int],
    ) -> Dict[str, bytes]:
        """
        Derive IKEv2 keys according to RFC 7296.

        Args:
            prf: PRF algorithm to use
            shared_secret: DH shared secret
            nonce_i: Initiator nonce
            nonce_r: Responder nonce
            spi_i: Initiator SPI (8 bytes)
            spi_r: Responder SPI (8 bytes)
            key_lengths: Dictionary of required key lengths

        Returns:
            Dictionary containing derived keys
        """
        # Concatenate nonces and SPIs
        ni_nr = nonce_i + nonce_r
        spi_ir = spi_i + spi_r

        # Compute SKEYSEED = prf(Ni | Nr, g^ir)
        skeyseed = prf.compute(ni_nr, shared_secret)

        # Derive keys using SKEYSEED
        keys = {}

        # Calculate total key material needed
        total_length = sum(key_lengths.values())

        # Generate key material: prf+(SKEYSEED, Ni | Nr | SPIi | SPIr)
        key_material = self._prf_plus(prf, skeyseed, ni_nr + spi_ir, total_length)

        # Extract individual keys
        offset = 0
        for key_name, length in key_lengths.items():
            keys[key_name] = key_material[offset : offset + length]
            offset += length

        return keys

    def derive_child_sa_keys(
        self,
        prf: PRFAlgorithm,
        sk_d: bytes,
        nonce_i: bytes,
        nonce_r: bytes,
        key_lengths: Dict[str, int],
    ) -> Dict[str, bytes]:
        """
        Derive Child SA keys for ESP.

        Args:
            prf: PRF algorithm to use
            sk_d: Key derivation key from IKE SA
            nonce_i: Initiator nonce from CREATE_CHILD_SA
            nonce_r: Responder nonce from CREATE_CHILD_SA
            key_lengths: Dictionary of required key lengths

        Returns:
            Dictionary containing derived Child SA keys
        """
        # Calculate total key material needed
        total_length = sum(key_lengths.values())

        # Generate key material: prf+(SK_d, Ni | Nr)
        key_material = self._prf_plus(prf, sk_d, nonce_i + nonce_r, total_length)

        # Extract individual keys
        keys = {}
        offset = 0
        for key_name, length in key_lengths.items():
            keys[key_name] = key_material[offset : offset + length]
            offset += length

        return keys

    def _prf_plus(
        self, prf: PRFAlgorithm, key: bytes, seed: bytes, length: int
    ) -> bytes:
        """
        PRF+ function as defined in RFC 7296.

        prf+(K, S) = T1 | T2 | T3 | T4 | ...
        where:
        T1 = prf(K, S | 0x01)
        T2 = prf(K, T1 | S | 0x02)
        T3 = prf(K, T2 | S | 0x03)
        ...
        """
        output = b""
        t_prev = b""
        counter = 1

        while len(output) < length:
            t_input = t_prev + seed + counter.to_bytes(1, "big")
            t_current = prf.compute(key, t_input)
            output += t_current
            t_prev = t_current
            counter += 1

        return output[:length]

    def derive_esp_keys(
        self, encryption_alg: str, integrity_alg: str, key_material: bytes
    ) -> Dict[str, bytes]:
        """
        Extract ESP encryption and integrity keys from key material.

        Args:
            encryption_alg: Encryption algorithm name
            integrity_alg: Integrity algorithm name
            key_material: Raw key material

        Returns:
            Dictionary with encryption and integrity keys
        """
        # Key lengths based on algorithms
        key_lengths = {
            "AES_GCM_16": {128: 16, 192: 24, 256: 32},
            "AES_CBC": {128: 16, 192: 24, 256: 32},
            "CHACHA20_POLY1305": {256: 32},
        }

        integrity_lengths = {
            "HMAC_SHA2_256": 32,
            "HMAC_SHA2_384": 48,
            "HMAC_SHA2_512": 64,
        }

        keys = {}
        offset = 0

        # Extract encryption key (assuming 256-bit for now)
        if encryption_alg in key_lengths:
            enc_len = key_lengths[encryption_alg].get(256, 32)
            keys["encryption"] = key_material[offset : offset + enc_len]
            offset += enc_len

        # Extract integrity key if needed (not for AEAD)
        if integrity_alg and integrity_alg in integrity_lengths:
            int_len = integrity_lengths[integrity_alg]
            keys["integrity"] = key_material[offset : offset + int_len]
            offset += int_len

        return keys
