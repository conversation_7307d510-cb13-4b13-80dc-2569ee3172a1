"""
IKEv2 state machine implementation.

This module provides a comprehensive state machine for IKEv2 protocol
that tracks the current state of exchanges and ensures proper protocol flow.
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Set, TYPE_CHECKING
from dataclasses import dataclass, field

from ...utils.logging import get_logger

if TYPE_CHECKING:
    from .protocol import IKEv2Role

logger = get_logger(__name__)


class ExchangeType(Enum):
    """IKEv2 exchange types."""

    IKE_SA_INIT = 34
    IKE_AUTH = 35
    CREATE_CHILD_SA = 36
    INFORMATIONAL = 37


class IKEv2State(Enum):
    """IKEv2 protocol states."""

    # Initial states
    INITIAL = "initial"

    # IKE_SA_INIT states
    SA_INIT_SENT = "sa_init_sent"
    SA_INIT_RECEIVED = "sa_init_received"
    SA_INIT_COMPLETED = "sa_init_completed"

    # IKE_AUTH states
    AUTH_SENT = "auth_sent"
    AUTH_RECEIVED = "auth_received"
    AUTH_COMPLETED = "auth_completed"

    # Established state
    ESTABLISHED = "established"

    # Child SA states
    CHILD_SA_CREATING = "child_sa_creating"
    CHILD_SA_ESTABLISHED = "child_sa_established"

    # Informational states
    INFORMATIONAL_SENT = "informational_sent"
    INFORMATIONAL_RECEIVED = "informational_received"

    # Error states
    ERROR = "error"
    TIMEOUT = "timeout"

    # Termination states
    DELETING = "deleting"
    DELETED = "deleted"


@dataclass
class StateTransition:
    """Represents a state transition."""

    from_state: IKEv2State
    to_state: IKEv2State
    trigger: str
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict = field(default_factory=dict)


class IKEv2StateMachine:
    """
    IKEv2 protocol state machine.

    This class manages the state transitions for IKEv2 protocol exchanges,
    ensuring that the protocol flow follows the correct sequence and
    providing hooks for state change events.
    """

    # Valid state transitions
    VALID_TRANSITIONS = {
        IKEv2State.INITIAL: {
            IKEv2State.SA_INIT_SENT,
            IKEv2State.SA_INIT_RECEIVED,
            IKEv2State.ERROR,
        },
        IKEv2State.SA_INIT_SENT: {
            IKEv2State.SA_INIT_RECEIVED,
            IKEv2State.SA_INIT_COMPLETED,
            IKEv2State.ERROR,
            IKEv2State.TIMEOUT,
        },
        IKEv2State.SA_INIT_RECEIVED: {
            IKEv2State.SA_INIT_COMPLETED,
            IKEv2State.AUTH_SENT,
            IKEv2State.ERROR,
        },
        IKEv2State.SA_INIT_COMPLETED: {
            IKEv2State.AUTH_SENT,
            IKEv2State.AUTH_RECEIVED,
            IKEv2State.ERROR,
        },
        IKEv2State.AUTH_SENT: {
            IKEv2State.AUTH_RECEIVED,
            IKEv2State.AUTH_COMPLETED,
            IKEv2State.ESTABLISHED,
            IKEv2State.ERROR,
            IKEv2State.TIMEOUT,
        },
        IKEv2State.AUTH_RECEIVED: {
            IKEv2State.AUTH_COMPLETED,
            IKEv2State.ESTABLISHED,
            IKEv2State.ERROR,
        },
        IKEv2State.AUTH_COMPLETED: {IKEv2State.ESTABLISHED, IKEv2State.ERROR},
        IKEv2State.ESTABLISHED: {
            IKEv2State.CHILD_SA_CREATING,
            IKEv2State.INFORMATIONAL_SENT,
            IKEv2State.INFORMATIONAL_RECEIVED,
            IKEv2State.DELETING,
            IKEv2State.ERROR,
        },
        IKEv2State.CHILD_SA_CREATING: {
            IKEv2State.CHILD_SA_ESTABLISHED,
            IKEv2State.ESTABLISHED,
            IKEv2State.ERROR,
        },
        IKEv2State.CHILD_SA_ESTABLISHED: {
            IKEv2State.ESTABLISHED,
            IKEv2State.CHILD_SA_CREATING,
            IKEv2State.DELETING,
            IKEv2State.ERROR,
        },
        IKEv2State.INFORMATIONAL_SENT: {
            IKEv2State.INFORMATIONAL_RECEIVED,
            IKEv2State.ESTABLISHED,
            IKEv2State.ERROR,
            IKEv2State.TIMEOUT,
        },
        IKEv2State.INFORMATIONAL_RECEIVED: {IKEv2State.ESTABLISHED, IKEv2State.ERROR},
        IKEv2State.DELETING: {IKEv2State.DELETED, IKEv2State.ERROR},
        IKEv2State.ERROR: {IKEv2State.INITIAL, IKEv2State.DELETED},
        IKEv2State.TIMEOUT: {IKEv2State.INITIAL, IKEv2State.ERROR, IKEv2State.DELETED},
        IKEv2State.DELETED: set(),  # Terminal state
    }

    def __init__(self, role: "IKEv2Role"):
        """
        Initialize the state machine.

        Args:
            role: The role of this peer (initiator or responder)
        """
        self.role = role
        self.current_state = IKEv2State.INITIAL
        self.previous_state: Optional[IKEv2State] = None

        # State history
        self.state_history: List[StateTransition] = []

        # Exchange tracking
        self.current_exchange: Optional[ExchangeType] = None
        self.exchange_history: List[ExchangeType] = []

        # Timing
        self.created_at = datetime.now()
        self.last_transition = datetime.now()

        logger.debug(f"IKEv2StateMachine initialized for {role.value}")

    def can_transition_to(self, new_state: IKEv2State) -> bool:
        """
        Check if transition to new state is valid.

        Args:
            new_state: The state to transition to

        Returns:
            True if transition is valid
        """
        valid_states = self.VALID_TRANSITIONS.get(self.current_state, set())
        return new_state in valid_states

    def transition_to(
        self,
        new_state: IKEv2State,
        trigger: str = "manual",
        metadata: Optional[Dict] = None,
    ) -> bool:
        """
        Transition to a new state.

        Args:
            new_state: The state to transition to
            trigger: What triggered this transition
            metadata: Additional metadata about the transition

        Returns:
            True if transition was successful
        """
        if not self.can_transition_to(new_state):
            logger.warning(
                f"Invalid state transition: {self.current_state.value} -> {new_state.value}"
            )
            return False

        # Record transition
        transition = StateTransition(
            from_state=self.current_state,
            to_state=new_state,
            trigger=trigger,
            metadata=metadata or {},
        )

        self.state_history.append(transition)

        # Update state
        self.previous_state = self.current_state
        self.current_state = new_state
        self.last_transition = datetime.now()

        logger.info(
            f"State transition: {self.previous_state.value} -> {new_state.value} "
            f"(trigger: {trigger})"
        )

        return True

    def start_exchange(self, exchange_type: ExchangeType) -> bool:
        """
        Start a new exchange.

        Args:
            exchange_type: Type of exchange to start

        Returns:
            True if exchange can be started
        """
        # Check if we can start this exchange in current state
        if not self._can_start_exchange(exchange_type):
            logger.warning(
                f"Cannot start {exchange_type.value} exchange in state {self.current_state.value}"
            )
            return False

        self.current_exchange = exchange_type
        self.exchange_history.append(exchange_type)

        # Transition to appropriate state based on exchange type and role
        if exchange_type == ExchangeType.IKE_SA_INIT:
            if self.role.value == "initiator":
                self.transition_to(
                    IKEv2State.SA_INIT_SENT, f"start_{exchange_type.value}"
                )
            else:
                self.transition_to(
                    IKEv2State.SA_INIT_RECEIVED, f"start_{exchange_type.value}"
                )

        elif exchange_type == ExchangeType.IKE_AUTH:
            if self.role.value == "initiator":
                self.transition_to(IKEv2State.AUTH_SENT, f"start_{exchange_type.value}")
            else:
                self.transition_to(
                    IKEv2State.AUTH_RECEIVED, f"start_{exchange_type.value}"
                )

        elif exchange_type == ExchangeType.CREATE_CHILD_SA:
            self.transition_to(
                IKEv2State.CHILD_SA_CREATING, f"start_{exchange_type.value}"
            )

        elif exchange_type == ExchangeType.INFORMATIONAL:
            if self.role.value == "initiator":
                self.transition_to(
                    IKEv2State.INFORMATIONAL_SENT, f"start_{exchange_type.value}"
                )
            else:
                self.transition_to(
                    IKEv2State.INFORMATIONAL_RECEIVED, f"start_{exchange_type.value}"
                )

        logger.info(f"Started {exchange_type.value} exchange")
        return True

    def complete_exchange(self, success: bool = True) -> bool:
        """
        Complete the current exchange.

        Args:
            success: Whether the exchange completed successfully

        Returns:
            True if exchange was completed
        """
        if not self.current_exchange:
            logger.warning("No current exchange to complete")
            return False

        exchange_type = self.current_exchange

        if success:
            # Transition to completion state based on exchange type
            if exchange_type == ExchangeType.IKE_SA_INIT:
                self.transition_to(
                    IKEv2State.SA_INIT_COMPLETED, f"complete_{exchange_type.value}"
                )

            elif exchange_type == ExchangeType.IKE_AUTH:
                self.transition_to(
                    IKEv2State.ESTABLISHED, f"complete_{exchange_type.value}"
                )

            elif exchange_type == ExchangeType.CREATE_CHILD_SA:
                self.transition_to(
                    IKEv2State.CHILD_SA_ESTABLISHED, f"complete_{exchange_type.value}"
                )

            elif exchange_type == ExchangeType.INFORMATIONAL:
                self.transition_to(
                    IKEv2State.ESTABLISHED, f"complete_{exchange_type.value}"
                )

        else:
            self.transition_to(IKEv2State.ERROR, f"failed_{exchange_type.value}")

        self.current_exchange = None
        logger.info(f"Completed {exchange_type.value} exchange (success: {success})")
        return True

    def _can_start_exchange(self, exchange_type: ExchangeType) -> bool:
        """Check if an exchange can be started in the current state."""

        if exchange_type == ExchangeType.IKE_SA_INIT:
            return self.current_state == IKEv2State.INITIAL

        elif exchange_type == ExchangeType.IKE_AUTH:
            return self.current_state in [
                IKEv2State.SA_INIT_COMPLETED,
                IKEv2State.SA_INIT_RECEIVED,
            ]

        elif exchange_type == ExchangeType.CREATE_CHILD_SA:
            return self.current_state in [
                IKEv2State.ESTABLISHED,
                IKEv2State.CHILD_SA_ESTABLISHED,
            ]

        elif exchange_type == ExchangeType.INFORMATIONAL:
            return self.current_state in [
                IKEv2State.ESTABLISHED,
                IKEv2State.CHILD_SA_ESTABLISHED,
            ]

        return False

    def get_state_duration(self) -> float:
        """Get the duration in current state (seconds)."""
        return (datetime.now() - self.last_transition).total_seconds()

    def get_total_duration(self) -> float:
        """Get the total duration since creation (seconds)."""
        return (datetime.now() - self.created_at).total_seconds()

    def is_established(self) -> bool:
        """Check if IKE SA is established."""
        return self.current_state in [
            IKEv2State.ESTABLISHED,
            IKEv2State.CHILD_SA_CREATING,
            IKEv2State.CHILD_SA_ESTABLISHED,
            IKEv2State.INFORMATIONAL_SENT,
            IKEv2State.INFORMATIONAL_RECEIVED,
        ]

    def is_error_state(self) -> bool:
        """Check if in an error state."""
        return self.current_state in [IKEv2State.ERROR, IKEv2State.TIMEOUT]

    def is_terminal_state(self) -> bool:
        """Check if in a terminal state."""
        return self.current_state == IKEv2State.DELETED

    def get_statistics(self) -> Dict:
        """Get state machine statistics."""
        return {
            "current_state": self.current_state.value,
            "previous_state": (
                self.previous_state.value if self.previous_state else None
            ),
            "current_exchange": (
                self.current_exchange.value if self.current_exchange else None
            ),
            "state_duration": self.get_state_duration(),
            "total_duration": self.get_total_duration(),
            "transition_count": len(self.state_history),
            "exchange_count": len(self.exchange_history),
            "is_established": self.is_established(),
            "is_error": self.is_error_state(),
            "is_terminal": self.is_terminal_state(),
        }

    def get_state_history(self) -> List[Dict]:
        """Get the complete state transition history."""
        return [
            {
                "from_state": t.from_state.value,
                "to_state": t.to_state.value,
                "trigger": t.trigger,
                "timestamp": t.timestamp.isoformat(),
                "metadata": t.metadata,
            }
            for t in self.state_history
        ]
