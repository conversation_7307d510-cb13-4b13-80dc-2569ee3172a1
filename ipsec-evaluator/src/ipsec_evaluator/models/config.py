"""
Configuration models for IPsec Evaluator.
"""

import os
import binascii
from pathlib import Path
from typing import List, Optional, Union, Tuple

from pydantic import Field, field_validator, IPvAnyAddress, TypeAdapter

from .base import ConfigModel

IntAdapter = TypeAdapter(int)
BoolAdapter = TypeAdapter(bool)

class GlobalConfig(ConfigModel):
    """Global application configuration."""

    timeout: int = Field(30, description="Default timeout in seconds", ge=1)
    verbose: bool = Field(False, description="Enable verbose logging")
    log_level: str = Field("INFO", description="Logging level")
    log_file: Optional[Path] = Field(None, description="Log file path")
    max_concurrent_tests: int = Field(5, description="Maximum concurrent tests", ge=1)

    @field_validator("log_level")
    @classmethod
    def validate_log_level(cls, v: str) -> str:
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of {valid_levels}")
        return v.upper()


class NetworkConfig(ConfigModel):
    """Network configuration for IPsec testing."""

    # Interface configuration
    interface: str = Field("eth0", description="Network interface")

    ipsec_src: IPvAnyAddress = Field(
        "***********", description="IPsec source IP"
    )
    ipsec_dst: IPvAnyAddress = Field(
        "********", description="IPsec destination IP"
    )
    ip_src: IPvAnyAddress = Field("***********", description="Source IP")
    ip_dst: IPvAnyAddress = Field("*********", description="Destination IP")

    port_src: int = Field(4500, description="Source port")
    port_dst: int = Field(4500, description="Destination port")

    # NAT traversal
    nat_traversal: bool = Field(True, description="Enable NAT traversal")

    # Network ranges
    test_network: str = Field("*************/24", description="Test network CIDR")

    @field_validator("ipsec_src", "ipsec_dst", "ip_src", "ip_dst")
    @classmethod
    def validate_ip_addresses(cls, v):
        """Validate IP addresses."""
        # IPvAnyAddress already handles validation
        return v

    @field_validator("port_src", "port_dst", mode="before")
    def port_in_range(cls, v):
        v = IntAdapter.validate_python(v)
        if not (0 <= v <= 65535):
            raise ValueError("Port must be between 0 and 65535")
        return v


class PKIConfig(ConfigModel):
    """PKI configuration for certificate-based authentication."""

    ca_cert_path: str = Field(..., description="CA certificate path")
    cert_path: str = Field(..., description="Certificate path")
    key_path: str = Field(..., description="Private key path")
    trust_chain: List[str] = Field(
        default_factory=list, description="Trust chain certificates"
    )
    verify_certificates: bool = Field(
        True, description="Enable certificate verification"
    )
    certificate_validation: bool = Field(
        True, description="Enable certificate validation"
    )

    @field_validator("ca_cert_path", "cert_path", "key_path", mode="after")
    def file_must_exist(cls, v):
        if not os.path.isfile(v):
            raise ValueError(f"File does not exist: {v}")
        return v

    @field_validator("trust_chain", mode="before")
    def validate_trustchain(cls, v):
        if not isinstance(v, list):
            if not os.path.isfile(v):
                raise ValueError(f"File does not exist: {v}")
            return [v]
        return v


class CryptoConfig(ConfigModel):
    """Simplified crypto configuration without external dependencies."""

    # Encryption algorithms (using standard names/IDs)
    encryption_algorithms: List[str] = Field(
        default=["AES-GCM-256"],
        description="Supported encryption algorithms"
    )

    # Integrity algorithms
    integrity_algorithms: List[str] = Field(
        default=["HMAC-SHA2-256"],
        description="Supported integrity algorithms"
    )

    # Diffie-Hellman groups
    dh_groups: List[int] = Field(
        default=[19],
        description="Supported DH groups"
    )

    # Pseudo-random functions
    prf_algorithms: List[str] = Field(
        default=["HMAC-SHA2-256"],
        description="Supported PRF algorithms"
    )

    @field_validator("dh_groups")
    @classmethod
    def validate_dh_groups(cls, v: List[int]) -> List[int]:
        """Validate DH group numbers."""
        valid_groups = [14, 15, 16, 17, 18, 19, 20, 21]  # Common DH groups
        for group in v:
            if group not in valid_groups:
                raise ValueError(f"Unsupported DH group: {group}")
        return v


class AuthConfig(ConfigModel):
    """Authentication configuration."""

    auth_method: str = Field(
        default="ECDSA_SECP256R1_SHA256",
        description="Authentication method"
    )

    certificate_auth: bool = Field(
        default=True,
        description="Use certificate-based authentication"
    )

    psk_auth: bool = Field(
        default=False,
        description="Use pre-shared key authentication"
    )

    @field_validator("auth_method")
    @classmethod
    def validate_auth_method(cls, v: str) -> str:
        """Validate authentication method."""
        valid_methods = [
            "ECDSA_SECP256R1_SHA256",
            "ECDSA_SECP384R1_SHA384",
            "RSA_PSS_SHA256",
            "RSA_PSS_SHA384",
            "PSK"
        ]
        if v not in valid_methods:
            raise ValueError(f"Invalid auth method: {v}. Must be one of {valid_methods}")
        return v


class TSConfig(ConfigModel):
    tsi_ip_range: Tuple[str, str] = Field(
        ("***********", "************"),
        description="Traffic Selector Initiator IP range",
        min_length=2,
    )
    tsi_port_range: Tuple[int, int] = Field(
        (0, 65535),
        description="Traffic Selector Initiator port range",
        min_length=2,
    )
    tsr_ip_range: Tuple[str, str] = Field(
        ("10.0.0.0", "**********"),
        description="Traffic Selector Responder IP range",
        min_length=2,
    )
    tsr_port_range: Tuple[int, int] = Field(
        (0, 65535),
        description="Traffic Selector Responder port range",
        min_length=2,
    )

    @field_validator("tsi_port_range", "tsr_port_range", mode="before")
    def validate_port_range(cls, v):
        # Accept input as either a tuple of strings or integers and convert to integers
        if isinstance(v, tuple) and len(v) == 2:
            return tuple(int(val) for val in v)
        # Accept input as string of the form port-port
        if isinstance(v, str) and "-" in v:
            return tuple(int(p) for p in v.split("-"))
        raise ValueError(
            "Port range must be a tuple with two values or a string of the form port-port"
        )

    @field_validator("tsi_ip_range", "tsr_ip_range", mode="before")
    def validate_ip_range(cls, v):
        if (
            isinstance(v, tuple)
            and isinstance(v[0], str)
            and isinstance(v[1], str)
        ):
            return v
        if isinstance(v, str) and "-" in v:
            return tuple(v.split("-"))
        raise ValueError(
            "IP range is either Tuple[str, str] or IPa-IPb, your value seems incorrect"
        )


class IDConfig(ConfigModel):
    """Identity configuration."""

    idi_type: str = Field(default="ID_FQDN", description="Initiator ID type")
    idi_data: str = Field(default="debian-client.lan", description="Initiator ID data")
    idr_type: str = Field(default="ID_FQDN", description="Responder ID type")
    idr_data: str = Field(default="strongswan-gw.lan", description="Responder ID data")

    @field_validator("idi_type", "idr_type")
    @classmethod
    def validate_id_types(cls, v: str) -> str:
        """Validate ID types."""
        valid_types = ["ID_FQDN", "ID_RFC822_ADDR", "ID_IPV4_ADDR", "ID_IPV6_ADDR", "ID_DER_ASN1_DN"]
        if v not in valid_types:
            raise ValueError(f"Invalid ID type: {v}. Must be one of {valid_types}")
        return v


class NATConfig(ConfigModel):
    nat_t: bool = Field(False, description="NAT_T enabled ?")
    nat_port_src: int = Field(4500, description="Port value for NAT src")
    nat_port_dst: int = Field(4500, description="Port value for NAT dst")

    @field_validator("nat_t", mode="before")
    def validate_nat_t(cls, v):
        return BoolAdapter.validate_python(v)


class NotifyPayload(ConfigModel):
    """Notify payload configuration."""

    notify_type: str = Field(
        description="Notify type name"
    )
    data: Optional[bytes] = Field(
        default=b"",
        description="Optional bytes data for the notify payload"
    )

    @field_validator("notify_type")
    @classmethod
    def validate_notify_type(cls, v: str) -> str:
        """Validate notify type."""
        # Common notify types
        valid_types = [
            "NAT_DETECTION_SOURCE_IP",
            "NAT_DETECTION_DESTINATION_IP",
            "COOKIE",
            "INVALID_SYNTAX",
            "NO_PROPOSAL_CHOSEN",
            "USE_TRANSPORT_MODE",
            "HTTP_CERT_LOOKUP_SUPPORTED",
            "REKEY_SA",
            "ESP_TFC_PADDING_NOT_SUPPORTED"
        ]
        if v not in valid_types:
            # Allow any string for extensibility
            pass
        return v

    @field_validator("data", mode="before")
    @classmethod
    def validate_data_field(cls, v):
        """Validate notify data field."""
        if v is None:
            return b""
        if isinstance(v, str):
            try:
                return binascii.unhexlify(v)
            except ValueError:
                raise ValueError("Notify payload must be a valid hex-encoded string")
        elif not isinstance(v, (bytes, bytearray)):
            raise TypeError("Notify field must be bytes or a hex-encoded string")
        return bytes(v)

    def __str__(self) -> str:
        return f"Notify {self.notify_type}: {binascii.hexlify(self.data).decode()}"


class IPsecConfig(ConfigModel):
    """Complete IPsec configuration."""

    crypto: CryptoConfig = Field(default_factory=CryptoConfig)
    auth: AuthConfig = Field(default_factory=AuthConfig)
    ts: TSConfig = Field(default_factory=TSConfig)
    identity: IDConfig = Field(default_factory=IDConfig)
    nat: NATConfig = Field(default_factory=NATConfig)

    # Protocol options
    childless: bool = Field(default=True, description="Childless SA")
    include_idr: bool = Field(default=False, description="Include IDr")
    include_init_contact: bool = Field(default=False, description="Initial contact")

    # Notify payloads for different exchanges
    notify_extras_init: Optional[List[NotifyPayload]] = Field(
        default=None, description="Notify payloads for INIT exchange"
    )
    notify_options_auth: Optional[List[NotifyPayload]] = Field(
        default=None, description="Notify payloads before SA in AUTH exchange"
    )
    notify_extras_auth: Optional[List[NotifyPayload]] = Field(
        default=None, description="Notify payloads for AUTH exchange"
    )
    notify_options_child: Optional[List[NotifyPayload]] = Field(
        default=None, description="Notify payloads before SA in CREATE_CHILD_SA"
    )
    notify_extras_child: Optional[List[NotifyPayload]] = Field(
        default=None, description="Notify payloads for CREATE_CHILD_SA"
    )

    @field_validator("childless", "include_idr", "include_init_contact", mode="before")
    @classmethod
    def validate_boolean_flags(cls, v):
        """Validate boolean flags."""
        return BoolAdapter.validate_python(v)


class ESPTests(ConfigModel):
    crypto: bool = Field(
        False,
        description="Test checks for invalid encrypted data, ICV and ciphered data",
    )
    replay: bool = Field(False, description="Test for replay handling")
    sp: bool = Field(False, description="Test for security policy")
    nat_t: bool = Field(False, description="Test for nat traversal, UDPENCAP")

    @field_validator("crypto", "replay", "sp", "nat_t", mode="before")
    def validate_tests(cls, v):
        return BoolAdapter.validate_python(v)


class ESPConfig(ConfigModel):
    tests: ESPTests = ESPTests()
    when: Union[int, bool] = Field(
        12,
        description="When ESP testing begins, use is to be matched against the SN",
    )


class TestConfiguration(ConfigModel):
    """Complete test configuration combining all components."""

    global_config: GlobalConfig = Field(default_factory=GlobalConfig)
    network: NetworkConfig = Field(default_factory=NetworkConfig)
    crypto: CryptoConfig = Field(default_factory=CryptoConfig)
    pki: Optional[PKIConfig] = Field(default=None)
