"""
IPsec Server Engine Implementation

This module provides the IPsec server (responder) implementation that combines
IKEv2 and ESP protocols to create a complete IPsec responder capable of
accepting secure tunnel connections and processing encrypted traffic.
"""

import asyncio
import secrets
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
from dataclasses import dataclass, field

from scapy.contrib.ikev2 import IKEv2
from scapy.layers.ipsec import ESP
from scapy.layers.inet import IP, UDP

from ..core.ikev2.protocol_updated import IKEv2Protocol, IKEv2Role, IKEv2SecurityAssociation
from ..core.esp.protocol import <PERSON>SPProtocol, ESPMode
from ..core.ikev2.state import IKEv2State, ExchangeType
from ..core.ikev2.hooks import IKEv2HookManager, HookType as IKEv2HookType
from ..models.base import BaseModel, TimestampedModel
from ..utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class ServerConfiguration:
    """Configuration for IPsec server."""
    
    # Network configuration
    listen_ip: str = "0.0.0.0"
    listen_port: int = 500
    
    # IPsec configuration
    tunnel_mode: bool = True
    nat_traversal: bool = False
    
    # Authentication
    auth_method: str = "ECDSA_SECP256R1_SHA256"
    certificate_path: Optional[str] = None
    private_key_path: Optional[str] = None
    
    # Crypto preferences
    encryption_algorithms: List[str] = field(default_factory=lambda: ["AES-GCM-256"])
    integrity_algorithms: List[str] = field(default_factory=lambda: ["HMAC-SHA2-256"])
    dh_groups: List[int] = field(default_factory=lambda: [19])
    
    # Server limits
    max_concurrent_connections: int = 100
    connection_timeout: float = 300.0
    keepalive_interval: float = 60.0
    
    # Security policies
    require_certificate_auth: bool = True
    allowed_client_subnets: List[str] = field(default_factory=list)


@dataclass
class ServerStatistics:
    """Statistics for IPsec server operations."""
    
    # Connection statistics
    total_connections: int = 0
    active_connections: int = 0
    successful_connections: int = 0
    failed_connections: int = 0
    rejected_connections: int = 0
    
    # IKE statistics
    ike_exchanges_received: int = 0
    ike_exchanges_processed: int = 0
    ike_exchanges_failed: int = 0
    
    # ESP statistics
    esp_packets_sent: int = 0
    esp_packets_received: int = 0
    esp_bytes_sent: int = 0
    esp_bytes_received: int = 0
    
    # Timing statistics
    average_connection_duration: float = 0.0
    server_start_time: Optional[datetime] = None
    
    # Error tracking
    last_error: Optional[str] = None
    error_count: int = 0


@dataclass
class ClientSession:
    """Represents an active client session."""
    
    client_ip: str
    client_port: int
    session_id: str
    protocol_instance: 'IPsecServerSession'
    created_at: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    is_authenticated: bool = False
    bytes_sent: int = 0
    bytes_received: int = 0


class IPsecServerSession(IKEv2Protocol, ESPProtocol):
    """
    IPsec Server Session (Per-Client Instance)
    
    This class represents a single client session on the server.
    Each connecting client gets its own instance of this class.
    """

    def __init__(
        self,
        client_ip: str,
        client_port: int,
        server_config: ServerConfiguration,
        ike_hook_manager: Optional[IKEv2HookManager] = None,
        esp_hook_manager: Optional[Any] = None,
    ):
        """
        Initialize a server session for a specific client.

        Args:
            client_ip: Client IP address
            client_port: Client port
            server_config: Server configuration
            ike_hook_manager: Optional IKE hook manager
            esp_hook_manager: Optional ESP hook manager
        """
        self.client_ip = client_ip
        self.client_port = client_port
        self.server_config = server_config
        self.session_id = f"{client_ip}:{client_port}_{secrets.token_hex(4)}"
        
        # Initialize parent protocols
        IKEv2Protocol.__init__(
            self,
            role=IKEv2Role.RESPONDER,
            local_ip=server_config.listen_ip,
            remote_ip=client_ip,
            local_port=server_config.listen_port,
            remote_port=client_port,
            hook_manager=ike_hook_manager,
        )
        
        ESPProtocol.__init__(
            self,
            mode=ESPMode.TUNNEL if server_config.tunnel_mode else ESPMode.TRANSPORT,
            hook_manager=esp_hook_manager,
        )
        
        # Session-specific state
        self.is_authenticated = False
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        
        logger.info(f"Server session created for client {client_ip}:{client_port}")

    async def handle_client_packet(self, packet: IKEv2, source_addr: Tuple[str, int]) -> Optional[IKEv2]:
        """
        Handle a packet from the client.
        
        Args:
            packet: The received IKEv2 packet
            source_addr: Source address tuple
            
        Returns:
            Response packet if one should be sent
        """
        self.last_activity = datetime.now()
        
        try:
            # Process the packet using inherited IKEv2Protocol methods
            response = await self.process_packet(packet, source_addr)
            
            # Update authentication status based on state
            if self.state_machine.current_state == IKEv2State.ESTABLISHED:
                self.is_authenticated = True
            
            return response
            
        except Exception as e:
            logger.error(f"Error handling client packet: {e}")
            raise

    async def send_data_to_client(self, data: bytes) -> bool:
        """
        Send data to the client through the IPsec tunnel.
        
        Args:
            data: Data to send
            
        Returns:
            True if data was sent successfully, False otherwise
        """
        if not self.is_authenticated:
            logger.error("Cannot send data: client not authenticated")
            return False
        
        try:
            # Create ESP packet (simplified)
            esp_packet = self._create_esp_packet(data)
            
            # In a real implementation, you would send this over the network
            logger.debug(f"Sent {len(data)} bytes to client {self.client_ip}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send data to client: {e}")
            return False

    def _create_esp_packet(self, data: bytes) -> ESP:
        """Create an ESP packet for the given data."""
        # This is a simplified implementation
        spi = secrets.token_bytes(4)
        seq = 1  # Would be managed by ESP protocol
        
        return ESP(spi=int.from_bytes(spi, 'big'), seq=seq) / data

    def get_session_info(self) -> Dict[str, Any]:
        """Get session information."""
        return {
            "session_id": self.session_id,
            "client_ip": self.client_ip,
            "client_port": self.client_port,
            "created_at": self.created_at.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "is_authenticated": self.is_authenticated,
            "ike_state": self.state_machine.current_state.value,
            "ike_sa_established": self.ike_sa is not None,
            "child_sas_count": len(self.child_sas),
        }


class IPsecServer:
    """
    IPsec Server (Responder) Engine
    
    This class manages multiple client connections and provides a complete
    IPsec server implementation. It listens for incoming connections,
    creates per-client sessions, and manages the overall server state.
    """

    def __init__(
        self,
        config: ServerConfiguration,
        ike_hook_manager: Optional[IKEv2HookManager] = None,
        esp_hook_manager: Optional[Any] = None,
    ):
        """
        Initialize the IPsec server.

        Args:
            config: Server configuration
            ike_hook_manager: Optional IKE hook manager
            esp_hook_manager: Optional ESP hook manager
        """
        self.config = config
        self.statistics = ServerStatistics()
        self.ike_hook_manager = ike_hook_manager
        self.esp_hook_manager = esp_hook_manager
        
        # Server state
        self.is_running = False
        self.client_sessions: Dict[str, ClientSession] = {}
        
        # Event handlers
        self.packet_handlers: List[Callable] = []
        self.connection_handlers: List[Callable] = []
        
        logger.info(f"IPsec Server initialized on {config.listen_ip}:{config.listen_port}")

    async def start(self) -> bool:
        """
        Start the IPsec server.
        
        Returns:
            True if server started successfully, False otherwise
        """
        if self.is_running:
            logger.warning("Server is already running")
            return True
        
        try:
            logger.info("Starting IPsec server...")
            
            # Initialize server state
            self.is_running = True
            self.statistics.server_start_time = datetime.now()
            
            # In a real implementation, you would:
            # 1. Bind to the listen address/port
            # 2. Start listening for UDP packets on port 500/4500
            # 3. Set up packet processing loop
            
            logger.info(f"IPsec server started on {self.config.listen_ip}:{self.config.listen_port}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start server: {e}")
            self.statistics.last_error = str(e)
            self.statistics.error_count += 1
            return False

    async def stop(self) -> bool:
        """
        Stop the IPsec server.
        
        Returns:
            True if server stopped successfully, False otherwise
        """
        if not self.is_running:
            logger.warning("Server is not running")
            return True
        
        try:
            logger.info("Stopping IPsec server...")
            
            # Disconnect all clients
            for session_key in list(self.client_sessions.keys()):
                await self.disconnect_client(session_key)
            
            # Stop server
            self.is_running = False
            
            logger.info("IPsec server stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping server: {e}")
            return False

    async def handle_incoming_packet(
        self, 
        packet: IKEv2, 
        source_addr: Tuple[str, int]
    ) -> Optional[IKEv2]:
        """
        Handle an incoming packet from a client.
        
        Args:
            packet: The received IKEv2 packet
            source_addr: Source address tuple (ip, port)
            
        Returns:
            Response packet if one should be sent
        """
        client_ip, client_port = source_addr
        session_key = f"{client_ip}:{client_port}"
        
        try:
            # Get or create client session
            if session_key not in self.client_sessions:
                if len(self.client_sessions) >= self.config.max_concurrent_connections:
                    logger.warning(f"Rejecting connection from {client_ip}: max connections reached")
                    self.statistics.rejected_connections += 1
                    return None
                
                # Create new session
                session = await self._create_client_session(client_ip, client_port)
                if not session:
                    return None
            else:
                session = self.client_sessions[session_key].protocol_instance
            
            # Process packet through session
            response = await session.handle_client_packet(packet, source_addr)
            
            # Update statistics
            self.statistics.ike_exchanges_received += 1
            if response:
                self.statistics.ike_exchanges_processed += 1
            
            return response
            
        except Exception as e:
            logger.error(f"Error handling packet from {client_ip}: {e}")
            self.statistics.ike_exchanges_failed += 1
            return None

    async def _create_client_session(self, client_ip: str, client_port: int) -> Optional[IPsecServerSession]:
        """Create a new client session."""
        try:
            # Check if client is allowed
            if not self._is_client_allowed(client_ip):
                logger.warning(f"Rejecting connection from {client_ip}: not in allowed subnets")
                self.statistics.rejected_connections += 1
                return None
            
            # Create session
            session_protocol = IPsecServerSession(
                client_ip=client_ip,
                client_port=client_port,
                server_config=self.config,
                ike_hook_manager=self.ike_hook_manager,
                esp_hook_manager=self.esp_hook_manager,
            )
            
            # Create session tracking
            session = ClientSession(
                client_ip=client_ip,
                client_port=client_port,
                session_id=session_protocol.session_id,
                protocol_instance=session_protocol,
            )
            
            session_key = f"{client_ip}:{client_port}"
            self.client_sessions[session_key] = session
            
            # Update statistics
            self.statistics.total_connections += 1
            self.statistics.active_connections += 1
            
            logger.info(f"Created new session for client {client_ip}:{client_port}")
            return session_protocol
            
        except Exception as e:
            logger.error(f"Failed to create session for {client_ip}: {e}")
            return None

    def _is_client_allowed(self, client_ip: str) -> bool:
        """Check if client IP is allowed to connect."""
        if not self.config.allowed_client_subnets:
            return True  # Allow all if no restrictions
        
        # In a real implementation, you would check against subnet masks
        return client_ip in self.config.allowed_client_subnets

    async def disconnect_client(self, session_key: str) -> bool:
        """Disconnect a specific client."""
        if session_key not in self.client_sessions:
            return False
        
        try:
            session = self.client_sessions[session_key]
            
            # Send disconnect notification
            await session.protocol_instance.send_informational_message("DELETE")
            
            # Remove session
            del self.client_sessions[session_key]
            self.statistics.active_connections -= 1
            
            logger.info(f"Disconnected client session {session_key}")
            return True
            
        except Exception as e:
            logger.error(f"Error disconnecting client {session_key}: {e}")
            return False

    def get_server_status(self) -> Dict[str, Any]:
        """Get current server status."""
        return {
            "running": self.is_running,
            "active_connections": self.statistics.active_connections,
            "total_connections": self.statistics.total_connections,
            "uptime": (
                (datetime.now() - self.statistics.server_start_time).total_seconds()
                if self.statistics.server_start_time else None
            ),
            "statistics": {
                "successful_connections": self.statistics.successful_connections,
                "failed_connections": self.statistics.failed_connections,
                "rejected_connections": self.statistics.rejected_connections,
                "ike_exchanges_received": self.statistics.ike_exchanges_received,
                "ike_exchanges_processed": self.statistics.ike_exchanges_processed,
                "esp_packets_sent": self.statistics.esp_packets_sent,
                "esp_packets_received": self.statistics.esp_packets_received,
            }
        }

    def get_client_sessions(self) -> List[Dict[str, Any]]:
        """Get information about all active client sessions."""
        return [
            session.protocol_instance.get_session_info()
            for session in self.client_sessions.values()
        ]

    def get_detailed_statistics(self) -> ServerStatistics:
        """Get detailed server statistics."""
        return self.statistics
