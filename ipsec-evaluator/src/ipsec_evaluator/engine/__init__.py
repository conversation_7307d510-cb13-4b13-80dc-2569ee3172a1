"""
Test execution engine for IPsec Evaluator.

This module contains the core test execution components:
- Orchestrator: Manages test execution and infrastructure
- Tester: Executes individual test scenarios
- Checker: Validates compliance and analyzes results
- Hook system: Provides callback capabilities
"""

from .orchestrator import EnhancedOrchestrator
from .tester import EnhancedTester
from .checker import <PERSON>han<PERSON><PERSON><PERSON><PERSON>
from .hooks import HookManager

# Backward compatibility aliases
Orchestrator = EnhancedOrchestrator
Tester = EnhancedTester
Checker = EnhancedChecker

__all__ = [
    "EnhancedOrchestrator",
    "EnhancedTester",
    "Enhanced<PERSON>he<PERSON>",
    "HookManager",
    "Orchestrator",  # Backward compatibility
    "Tester",  # Backward compatibility
    "Checker",  # Backward compatibility
]
