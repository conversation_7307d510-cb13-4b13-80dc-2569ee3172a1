"""
Enhanced test orchestrator for managing IPsec compliance testing.

The Orchestrator coordinates the entire testing process with:
- Comprehensive hook system integration
- Advanced progress tracking and reporting
- Resource management and concurrency control
- Rich metadata collection and analysis
- Error handling and recovery mechanisms
"""

import asyncio
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from uuid import uuid4
from dataclasses import dataclass, field
from enum import Enum

from ..models.base import TestStatus, TestMode
from ..models.config import TestConfiguration
from ..models.scenario import TestScenario
from ..models.tests import TestResult, ComplianceReport
from ..utils.logging import get_logger
from .tester import Tester
from .checker import Checker

from .hooks import HookManager

logger = get_logger(__name__)


class ExecutionPhase(Enum):
    """Test execution phases."""

    INITIALIZATION = "initialization"
    SCENARIO_LOADING = "scenario_loading"
    TEST_EXECUTION = "test_execution"
    COMPLIANCE_CHECKING = "compliance_checking"
    REPORT_GENERATION = "report_generation"
    COMPLETED = "completed"


@dataclass
class ExecutionContext:
    """Context information for test execution."""

    orchestrator_id: str
    start_time: datetime
    current_phase: ExecutionPhase = ExecutionPhase.INITIALIZATION
    total_scenarios: int = 0
    completed_tests: int = 0
    failed_tests: int = 0
    active_tests: Dict[str, str] = field(
        default_factory=dict
    )  # test_id -> scenario_name
    metadata: Dict[str, Any] = field(default_factory=dict)


class EnhancedOrchestrator:
    """
    Enhanced orchestrator for IPsec compliance testing.

    Provides comprehensive test management with:
    - Hook system integration for extensibility
    - Advanced progress tracking and reporting
    - Resource management and concurrency control
    - Rich metadata collection and analysis
    - Error handling and recovery mechanisms
    - Real-time status monitoring
    """

    def __init__(self, config: TestConfiguration, hook_manager: Optional[HookManager] = None):
        """
        Initialize the enhanced orchestrator.

        Args:
            config: Test configuration
            hook_manager: Optional hook manager for callbacks
        """
        self.config = config
        self.hook_manager = hook_manager or HookManager()

        # Execution state
        self.orchestrator_id = str(uuid4())
        self.execution_context = ExecutionContext(
            orchestrator_id=self.orchestrator_id, start_time=datetime.now()
        )

        # Test management
        self.test_results: List[TestResult] = []
        self.active_tests: Dict[str, asyncio.Task] = {}
        self._semaphore = asyncio.Semaphore(config.global_config.max_concurrent_tests)

        # Statistics
        self.statistics = {
            "tests_executed": 0,
            "tests_passed": 0,
            "tests_failed": 0,
            "total_execution_time": 0.0,
            "average_test_time": 0.0,
            "hook_executions": 0,
            "scenarios_loaded": 0,
            "compliance_checks": 0,
        }

        logger.info(f"Enhanced Orchestrator initialized (ID: {self.orchestrator_id})")

    async def execute_scenarios(
        self,
        scenario_names: List[str],
        mode: str = "both",
        progress_callback: Optional[Callable] = None,
    ) -> List[TestResult]:
        """
        Execute test scenarios with comprehensive tracking and hook integration.

        Args:
            scenario_names: List of scenario names to execute
            mode: Test mode (initiator, responder, both)
            progress_callback: Optional callback for progress updates

        Returns:
            List of test results
        """
        execution_start = datetime.now()

        logger.info(
            f"Starting execution of {len(scenario_names)} scenarios in {mode} mode"
        )

        try:
            # Phase 1: Initialization
            await self._execute_phase_hooks(
                "pre_execution",
                {
                    "scenario_names": scenario_names,
                    "mode": mode,
                    "orchestrator_id": self.orchestrator_id,
                },
            )

            # Phase 2: Load scenarios
            self.execution_context.current_phase = ExecutionPhase.SCENARIO_LOADING
            scenarios = await self._load_scenarios_async(scenario_names)
            self.statistics["scenarios_loaded"] = len(scenarios)

            # Phase 3: Execute tests
            self.execution_context.current_phase = ExecutionPhase.TEST_EXECUTION
            self.execution_context.total_scenarios = len(scenarios)

            results = await self._execute_scenarios_async(
                scenarios, mode, progress_callback
            )

            # Phase 4: Generate compliance report
            self.execution_context.current_phase = ExecutionPhase.REPORT_GENERATION
            await self._generate_compliance_report_async(results)

            # Phase 5: Completion
            self.execution_context.current_phase = ExecutionPhase.COMPLETED
            execution_time = (datetime.now() - execution_start).total_seconds()
            self.statistics["total_execution_time"] = execution_time

            await self._execute_phase_hooks(
                "post_execution",
                {
                    "results": results,
                    "execution_time": execution_time,
                    "statistics": self.statistics,
                },
            )

            logger.info(
                f"Execution completed: {len(results)} tests in {execution_time:.2f}s"
            )
            return results

        except Exception as e:
            logger.error(f"Execution failed: {e}")
            await self._execute_phase_hooks(
                "execution_error",
                {"error": str(e), "phase": self.execution_context.current_phase.value},
            )
            raise

    def execute_scenarios_sync(
        self, scenario_names: List[str], mode: str = "both"
    ) -> List[TestResult]:
        """
        Synchronous wrapper for execute_scenarios.

        Args:
            scenario_names: List of scenario names to execute
            mode: Test mode (initiator, responder, both)

        Returns:
            List of test results
        """
        return asyncio.run(self.execute_scenarios(scenario_names, mode))

    async def _execute_scenarios_async(
        self, scenarios: List[TestScenario], mode: str
    ) -> List[TestResult]:
        """Execute scenarios asynchronously."""
        tasks = []

        for scenario in scenarios:
            if mode in ["initiator", "both"] and scenario.has_initiator_tests():
                task = self._create_test_task(scenario, TestMode.INITIATOR)
                tasks.append(task)

            if mode in ["responder", "both"] and scenario.has_responder_tests():
                task = self._create_test_task(scenario, TestMode.RESPONDER)
                tasks.append(task)

        # Execute all tests
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Filter out exceptions and log them
        valid_results = []
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"Test execution failed: {result}")
            else:
                valid_results.append(result)

        return valid_results

    async def _create_test_task(
        self, scenario: TestScenario, mode: TestMode
    ) -> TestResult:
        """Create and execute a test task."""
        async with self._semaphore:
            return await self._execute_single_test(scenario, mode)

    async def _execute_single_test(
        self, scenario: TestScenario, mode: TestMode
    ) -> TestResult:
        """Execute a single test scenario."""
        test_id = str(uuid4())
        start_time = datetime.utcnow()

        logger.info(f"Starting test {test_id}: {scenario.name} ({mode})")

        try:
            # Create tester and checker
            tester = Tester(
                test_id=test_id, config=self.config, scenario=scenario, mode=mode
            )

            checker = Checker(test_id=test_id, config=self.config, scenario=scenario)

            # Execute test
            test_data = await tester.execute()

            # Check compliance
            compliance_result = await checker.check_compliance(test_data)

            # Create result
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()

            result = TestResult(
                test_id=test_id,
                scenario_name=scenario.name,
                mode=mode.value,
                status=TestStatus.COMPLETED,
                compliance=compliance_result.level,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                test_data=test_data,
                compliance_details=compliance_result,
            )

            logger.info(f"Test {test_id} completed: {compliance_result.level}")
            return result

        except Exception as e:
            logger.error(f"Test {test_id} failed: {e}")

            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()

            return TestResult(
                test_id=test_id,
                scenario_name=scenario.name,
                mode=mode.value,
                status=TestStatus.FAILED,
                compliance="unknown",
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                error_message=str(e),
            )

    def _load_scenarios(self, scenario_names: List[str]) -> List[TestScenario]:
        """Load test scenarios from files."""
        scenarios = []
        scenarios_dir = Path("scenarios")

        for name in scenario_names:
            scenario_file = scenarios_dir / f"{name}.yml"
            if not scenario_file.exists():
                scenario_file = scenarios_dir / f"{name}.yaml"

            if not scenario_file.exists():
                logger.error(f"Scenario file not found: {name}")
                continue

            try:
                scenario = TestScenario.from_file(scenario_file)
                scenarios.append(scenario)
                logger.info(f"Loaded scenario: {scenario.name}")
            except Exception as e:
                logger.error(f"Failed to load scenario {name}: {e}")

        return scenarios

    def _generate_compliance_report(self, results: List[TestResult]) -> None:
        """Generate and save compliance report."""
        report = ComplianceReport(
            generated_at=datetime.utcnow(),
            total_tests=len(results),
            passed_tests=len([r for r in results if r.compliance == "compliant"]),
            failed_tests=len([r for r in results if r.compliance == "non_compliant"]),
            results=results,
        )

        # Save report
        report_file = (
            self.config.results_dir
            / f"compliance_report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
        )
        report.save_to_file(report_file)

        logger.info(f"Compliance report saved: {report_file}")
        logger.info(f"Test summary: {report.passed_tests}/{report.total_tests} passed")

    async def _execute_phase_hooks(self, phase: str, context: Dict[str, Any]) -> None:
        """Execute hooks for a specific phase."""
        if self.hook_manager:
            try:
                from .hooks import HookContext
                from ..models.base import HookType

                hook_context = HookContext(
                    hook_type=HookType.PRE_EXCHANGE,  # Generic hook type
                    metadata=context
                )
                await self.hook_manager.execute_hooks(HookType.PRE_EXCHANGE, hook_context)
            except Exception as e:
                logger.warning(f"Hook execution failed for phase {phase}: {e}")

    async def _load_scenarios_async(self, scenario_names: List[str]) -> List[TestScenario]:
        """Asynchronously load test scenarios."""
        return self._load_scenarios(scenario_names)

    async def _generate_compliance_report_async(self, results: List[TestResult]) -> None:
        """Asynchronously generate compliance report."""
        self._generate_compliance_report(results)
