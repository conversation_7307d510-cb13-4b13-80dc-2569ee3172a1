"""
Scenario parsing and management module for IPsec Evaluator.

This module provides comprehensive scenario parsing capabilities that build upon
the ipsecdr design but modernize it with Pydantic models, enhanced validation,
and support for the new hook system and configuration architecture.
"""

from .parser import TestScenarioParser, TestParser, ScenarioParsingError
from .loader import ScenarioLoader, ScenarioRegistry
from .validator import ScenarioValidator, ValidationError

__all__ = [
    # Core parsing
    "TestScenarioParser",
    "TestParser", 
    "ScenarioParsingError",
    
    # Loading and registry
    "ScenarioLoader",
    "ScenarioRegistry",
    
    # Validation
    "ScenarioValidator",
    "ValidationError",
]
