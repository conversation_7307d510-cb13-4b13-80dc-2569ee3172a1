"""
Enhanced configuration parser for ipsec-evaluator.

This module provides comprehensive configuration management with:
- Modern Pydantic v2 support with enhanced validation
- TOML, YAML, and INI format support
- Environment variable integration
- CLI argument overrides
- Configuration validation and schema generation
- Hot-reload capabilities
- Configuration templates and presets
"""

import os
import configparser
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Type
import toml
import yaml
from pydantic import ValidationError
from pydantic_settings import BaseSettings, SettingsConfigDict

from ..models.config import (
    TestConfiguration,
    GlobalConfig,
    NetworkConfig,
    CryptoConfig,
    PKIConfig
)
from ..utils.logging import get_logger

logger = get_logger(__name__)


class ConfigurationError(Exception):
    """Configuration-related errors."""
    pass


class IPsecEvaluatorConfigParser:
    """
    Enhanced configuration parser for ipsec-evaluator.

    Supports multiple configuration formats and provides comprehensive
    configuration management with validation, overrides, and templates.
    """

    SUPPORTED_FORMATS = {'.toml', '.yaml', '.yml', '.ini', '.cfg'}

    def __init__(
        self,
        config_files: Union[str, List[str], Path, List[Path]] = None,
        cli_args: Optional[Dict[str, Any]] = None,
        env_prefix: str = "IPSEC_EVALUATOR",
        validate_config: bool = True
    ):
        """
        Initialize the configuration parser.

        Args:
            config_files: Configuration file(s) to load
            cli_args: CLI argument overrides
            env_prefix: Environment variable prefix
            validate_config: Whether to validate configuration
        """
        self.env_prefix = env_prefix
        self.validate_config = validate_config
        self.config_files = self._normalize_config_files(config_files)

        # Initialize configuration objects
        self.global_config = GlobalConfig()
        self.network = NetworkConfig()
        self.crypto = CryptoConfig()
        self.pki: Optional[PKIConfig] = None

        # Load configurations in order of precedence
        self._load_configurations()

        # Apply CLI overrides if provided
        if cli_args:
            self.apply_cli_overrides(cli_args)

        # Apply environment variable overrides
        self._apply_env_overrides()

        # Validate final configuration
        if self.validate_config:
            self._validate_configuration()

        logger.info(f"Configuration loaded from {len(self.config_files)} files")

    def _normalize_config_files(
        self,
        config_files: Union[str, List[str], Path, List[Path], None]
    ) -> List[Path]:
        """Normalize configuration files to a list of Path objects."""
        if config_files is None:
            return []

        if isinstance(config_files, (str, Path)):
            config_files = [config_files]

        normalized = []
        for config_file in config_files:
            path = Path(config_file)
            if path.exists():
                normalized.append(path)
            else:
                logger.warning(f"Configuration file not found: {path}")

        return normalized

    def _load_configurations(self):
        """Load configurations from all specified files."""
        for config_file in self.config_files:
            try:
                self._load_single_config(config_file)
                logger.debug(f"Loaded configuration from {config_file}")
            except Exception as e:
                logger.error(f"Failed to load configuration from {config_file}: {e}")
                if self.validate_config:
                    raise ConfigurationError(f"Failed to load {config_file}: {e}")

    def _load_single_config(self, config_file: Path):
        """Load configuration from a single file."""
        suffix = config_file.suffix.lower()

        if suffix == '.toml':
            self._load_toml_config(config_file)
        elif suffix in {'.yaml', '.yml'}:
            self._load_yaml_config(config_file)
        elif suffix in {'.ini', '.cfg'}:
            self._load_ini_config(config_file)
        else:
            raise ConfigurationError(f"Unsupported configuration format: {suffix}")

    def _load_toml_config(self, config_file: Path):
        """Load TOML configuration file."""
        with open(config_file, 'r') as f:
            config_data = toml.load(f)

        self._apply_config_data(config_data)

    def _load_yaml_config(self, config_file: Path):
        """Load YAML configuration file."""
        with open(config_file, 'r') as f:
            config_data = yaml.safe_load(f)

        if config_data:
            self._apply_config_data(config_data)

    def _load_ini_config(self, config_file: Path):
        """Load INI configuration file (ipsecdr compatibility)."""
        config = configparser.ConfigParser()
        config.read(config_file)

        # Convert INI sections to nested dictionary
        config_data = {}
        for section_name in config.sections():
            section_data = dict(config.items(section_name))

            # Map ipsecdr section names to ipsec-evaluator structure
            if section_name.upper() == 'ARGS':
                config_data['global_config'] = section_data
            elif section_name.upper() == 'IF':
                config_data['network'] = section_data
            elif section_name.upper() == 'CA':
                config_data['pki'] = section_data
            elif section_name.upper() in ['IKE_SA', 'CHILD_SA', 'AUTH']:
                if 'crypto' not in config_data:
                    config_data['crypto'] = {}
                config_data['crypto'].update(self._map_crypto_section(section_name, section_data))

        self._apply_config_data(config_data)

    def _map_crypto_section(self, section_name: str, section_data: Dict[str, str]) -> Dict[str, Any]:
        """Map ipsecdr crypto sections to ipsec-evaluator format."""
        mapped_data = {}

        if section_name.upper() == 'IKE_SA':
            if 'encr' in section_data:
                mapped_data['ike_encryption'] = self._parse_list_value(section_data['encr'])
            if 'integ' in section_data:
                mapped_data['ike_integrity'] = self._parse_list_value(section_data['integ'])
            if 'prf' in section_data:
                mapped_data['ike_prf'] = self._parse_list_value(section_data['prf'])
            if 'groupdesc' in section_data:
                mapped_data['ike_dh_groups'] = self._parse_list_value(section_data['groupdesc'])
            if 'encr_size' in section_data:
                mapped_data['encryption_key_sizes'] = self._parse_list_value(section_data['encr_size'])

        elif section_name.upper() == 'CHILD_SA':
            if 'encr' in section_data:
                mapped_data['esp_encryption'] = self._parse_list_value(section_data['encr'])
            if 'integ' in section_data:
                mapped_data['esp_integrity'] = self._parse_list_value(section_data['integ'])

        return mapped_data

    def _parse_list_value(self, value: str) -> List[Any]:
        """Parse comma-separated list values."""
        if isinstance(value, str):
            # Handle both comma and space separated values
            items = [item.strip() for item in value.replace(',', ' ').split()]
            # Try to convert to integers if possible
            result = []
            for item in items:
                try:
                    result.append(int(item))
                except ValueError:
                    result.append(item)
            return result
        return [value]

    def _apply_config_data(self, config_data: Dict[str, Any]):
        """Apply configuration data to configuration objects."""
        if 'global_config' in config_data:
            self._update_config_object(self.global_config, config_data['global_config'])

        if 'network' in config_data:
            self._update_config_object(self.network, config_data['network'])

        if 'crypto' in config_data:
            self._update_config_object(self.crypto, config_data['crypto'])

        if 'pki' in config_data:
            if self.pki is None:
                self.pki = PKIConfig(**config_data['pki'])
            else:
                self._update_config_object(self.pki, config_data['pki'])

    def _update_config_object(self, config_obj: Any, updates: Dict[str, Any]):
        """Update a configuration object with new values."""
        for key, value in updates.items():
            if hasattr(config_obj, key):
                try:
                    # Handle type conversion for Pydantic models
                    current_value = getattr(config_obj, key)
                    if isinstance(current_value, bool) and isinstance(value, str):
                        value = value.lower() in ('true', '1', 'yes', 'on')
                    elif isinstance(current_value, int) and isinstance(value, str):
                        value = int(value)
                    elif isinstance(current_value, float) and isinstance(value, str):
                        value = float(value)

                    setattr(config_obj, key, value)
                except (ValueError, TypeError) as e:
                    logger.warning(f"Failed to set {key}={value}: {e}")
            else:
                logger.warning(f"Unknown configuration key: {key}")

    def _apply_env_overrides(self):
        """Apply environment variable overrides."""
        env_vars = {k: v for k, v in os.environ.items() if k.startswith(self.env_prefix)}

        for env_key, env_value in env_vars.items():
            # Remove prefix and convert to config path
            config_key = env_key[len(self.env_prefix):].lstrip('_').lower()

            # Handle nested configuration keys (e.g., IPSEC_EVALUATOR_NETWORK_INITIATOR_IP)
            if '.' in config_key or '_' in config_key:
                self._apply_nested_env_override(config_key, env_value)

    def _apply_nested_env_override(self, config_key: str, env_value: str):
        """Apply nested environment variable override."""
        # Convert underscores to dots for nested access
        key_parts = config_key.replace('_', '.').split('.')

        if len(key_parts) >= 2:
            section = key_parts[0]
            field = '.'.join(key_parts[1:])

            if section == 'global':
                self._set_nested_value(self.global_config, field, env_value)
            elif section == 'network':
                self._set_nested_value(self.network, field, env_value)
            elif section == 'crypto':
                self._set_nested_value(self.crypto, field, env_value)
            elif section == 'pki' and self.pki:
                self._set_nested_value(self.pki, field, env_value)

    def _set_nested_value(self, obj: Any, field_path: str, value: str):
        """Set a nested value on an object."""
        field_parts = field_path.split('.')
        current_obj = obj

        for part in field_parts[:-1]:
            if hasattr(current_obj, part):
                current_obj = getattr(current_obj, part)
            else:
                return  # Path doesn't exist

        final_field = field_parts[-1]
        if hasattr(current_obj, final_field):
            try:
                # Type conversion
                current_value = getattr(current_obj, final_field)
                if isinstance(current_value, bool):
                    value = value.lower() in ('true', '1', 'yes', 'on')
                elif isinstance(current_value, int):
                    value = int(value)
                elif isinstance(current_value, float):
                    value = float(value)
                elif isinstance(current_value, list):
                    value = self._parse_list_value(value)

                setattr(current_obj, final_field, value)
                logger.debug(f"Applied env override: {field_path}={value}")
            except (ValueError, TypeError) as e:
                logger.warning(f"Failed to apply env override {field_path}={value}: {e}")

    def apply_cli_overrides(self, cli_args: Dict[str, Any]):
        """Apply command-line argument overrides."""
        for arg_key, arg_value in cli_args.items():
            if '.' in arg_key:
                # Handle nested keys (e.g., "network.initiator_ip")
                section, field = arg_key.split('.', 1)

                if section.lower() == 'global':
                    self._set_nested_value(self.global_config, field, str(arg_value))
                elif section.lower() == 'network':
                    self._set_nested_value(self.network, field, str(arg_value))
                elif section.lower() == 'crypto':
                    self._set_nested_value(self.crypto, field, str(arg_value))
                elif section.lower() == 'pki' and self.pki:
                    self._set_nested_value(self.pki, field, str(arg_value))
                else:
                    logger.warning(f"Unknown configuration section: {section}")
            else:
                logger.warning(f"CLI override key must contain section: {arg_key}")

    def _validate_configuration(self):
        """Validate the final configuration."""
        try:
            # Create a complete configuration object for validation
            config_dict = {
                'global_config': self.global_config.model_dump(),
                'network': self.network.model_dump(),
                'crypto': self.crypto.model_dump(),
            }

            if self.pki:
                config_dict['pki'] = self.pki.model_dump()

            # Validate using TestConfiguration
            TestConfiguration(**config_dict)
            logger.debug("Configuration validation successful")

        except ValidationError as e:
            logger.error(f"Configuration validation failed: {e}")
            if self.validate_config:
                raise ConfigurationError(f"Configuration validation failed: {e}")

    def get_test_configuration(self) -> TestConfiguration:
        """Get a complete TestConfiguration object."""
        return TestConfiguration(
            global_config=self.global_config,
            network=self.network,
            crypto=self.crypto,
            pki=self.pki
        )

    def overlay_config(self, overlay_files: List[Union[str, Path]]):
        """Overlay additional configuration files."""
        overlay_files = self._normalize_config_files(overlay_files)

        for overlay_file in overlay_files:
            try:
                self._load_single_config(overlay_file)
                logger.debug(f"Applied overlay configuration from {overlay_file}")
            except Exception as e:
                logger.error(f"Failed to apply overlay from {overlay_file}: {e}")
                if self.validate_config:
                    raise ConfigurationError(f"Failed to apply overlay {overlay_file}: {e}")

    def save_config(self, output_file: Path, format_type: str = 'toml'):
        """Save current configuration to file."""
        config_dict = {
            'global_config': self.global_config.model_dump(),
            'network': self.network.model_dump(),
            'crypto': self.crypto.model_dump(),
        }

        if self.pki:
            config_dict['pki'] = self.pki.model_dump()

        if format_type.lower() == 'toml':
            with open(output_file, 'w') as f:
                toml.dump(config_dict, f)
        elif format_type.lower() in {'yaml', 'yml'}:
            with open(output_file, 'w') as f:
                yaml.dump(config_dict, f, default_flow_style=False)
        else:
            raise ConfigurationError(f"Unsupported output format: {format_type}")

        logger.info(f"Configuration saved to {output_file}")

    def get_config_summary(self) -> Dict[str, Any]:
        """Get a summary of the current configuration."""
        return {
            'config_files': [str(f) for f in self.config_files],
            'global_config': self.global_config.model_dump(),
            'network': self.network.model_dump(),
            'crypto': self.crypto.model_dump(),
            'pki': self.pki.model_dump() if self.pki else None,
            'validation_enabled': self.validate_config,
            'env_prefix': self.env_prefix
        }

    @classmethod
    def create_template(cls, template_type: str = 'basic') -> Dict[str, Any]:
        """Create a configuration template."""
        templates = {
            'basic': cls._create_basic_template(),
            'anssi': cls._create_anssi_template(),
            'performance': cls._create_performance_template(),
            'development': cls._create_development_template()
        }

        if template_type not in templates:
            raise ConfigurationError(f"Unknown template type: {template_type}")

        return templates[template_type]

    @staticmethod
    def _create_basic_template() -> Dict[str, Any]:
        """Create a basic configuration template."""
        return {
            'global_config': {
                'max_concurrent_tests': 2,
                'timeout': 300,
                'verbose': False,
                'log_level': 'INFO'
            },
            'network': {
                'interface': 'eth0',
                'ipsec_src': '************',
                'ipsec_dst': '************',
                'ip_src': '************',
                'ip_dst': '************',
                'port_src': 500,
                'port_dst': 500,
                'nat_traversal': False,
                'test_network': '***********/24'
            },
            'crypto': {
                'encryption_algorithms': ['AES-GCM-256'],
                'integrity_algorithms': ['HMAC-SHA2-256'],
                'prf_algorithms': ['HMAC-SHA2-256'],
                'dh_groups': [19]  # secp256r1
            }
        }

    @staticmethod
    def _create_anssi_template() -> Dict[str, Any]:
        """Create an ANSSI-compliant configuration template."""
        return {
            'global_config': {
                'max_concurrent_tests': 2,
                'timeout': 300,
                'verbose': True,
                'log_level': 'DEBUG'
            },
            'network': {
                'interface': 'eth0',
                'ipsec_src': '************',
                'ipsec_dst': '************',
                'ip_src': '************',
                'ip_dst': '************',
                'port_src': 500,
                'port_dst': 500,
                'nat_traversal': False,
                'test_network': '***********/24'
            },
            'crypto': {
                'encryption_algorithms': ['AES-GCM-256', 'AES-CTR'],
                'integrity_algorithms': ['HMAC-SHA2-256', 'HMAC-SHA2-384'],
                'prf_algorithms': ['HMAC-SHA2-256', 'HMAC-SHA2-384'],
                'dh_groups': [19, 20, 21]  # secp256r1, secp384r1, secp521r1
            },
            'pki': {
                'ca_cert_path': '/etc/ipsec.d/cacerts/ca.pem',
                'cert_path': '/etc/ipsec.d/certs/client.pem',
                'key_path': '/etc/ipsec.d/private/client.pem',
                'verify_certificates': True,
                'certificate_validation': True
            }
        }

    @staticmethod
    def _create_performance_template() -> Dict[str, Any]:
        """Create a performance testing configuration template."""
        return {
            'global_config': {
                'max_concurrent_tests': 8,
                'test_timeout': 600,
                'results_dir': './performance_results',
                'verbose': False,
                'log_level': 'WARNING'
            },
            'network': {
                'initiator_ip': '************',
                'responder_ip': '************',
                'ike_port': 500,
                'nat_traversal': False,
                'interface': 'eth0'
            },
            'crypto': {
                'ike_encryption': ['AES_GCM_16', 'AES_CTR', 'CHACHA20_POLY1305'],
                'ike_integrity': ['HMAC_SHA2_256'],
                'ike_prf': ['PRF_HMAC_SHA2_256'],
                'ike_dh_groups': [19, 20, 21, 31],  # Multiple curves for testing
                'esp_encryption': ['AES_GCM_16', 'AES_CTR'],
                'esp_integrity': ['HMAC_SHA2_256'],
                'encryption_key_sizes': [256]
            }
        }

    @staticmethod
    def _create_development_template() -> Dict[str, Any]:
        """Create a development configuration template."""
        return {
            'global_config': {
                'max_concurrent_tests': 1,
                'test_timeout': 60,
                'results_dir': './dev_results',
                'verbose': True,
                'log_level': 'DEBUG'
            },
            'network': {
                'initiator_ip': '127.0.0.1',
                'responder_ip': '127.0.0.1',
                'ike_port': 5000,  # Non-privileged port for development
                'nat_traversal': False,
                'interface': 'lo'
            },
            'crypto': {
                'ike_encryption': ['AES_GCM_16'],
                'ike_integrity': ['HMAC_SHA2_256'],
                'ike_prf': ['PRF_HMAC_SHA2_256'],
                'ike_dh_groups': [19],  # Simple for development
                'esp_encryption': ['AES_GCM_16'],
                'esp_integrity': ['HMAC_SHA2_256'],
                'encryption_key_sizes': [256]
            }
        }

    @classmethod
    def from_template(
        cls,
        template_type: str = 'basic',
        overrides: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> 'IPsecEvaluatorConfigParser':
        """Create a configuration parser from a template."""
        template_data = cls.create_template(template_type)

        # Apply overrides if provided
        if overrides:
            cls._deep_update(template_data, overrides)

        # Create a temporary config file
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            toml.dump(template_data, f)
            temp_config_file = f.name

        try:
            # Create parser with template
            parser = cls(config_files=[temp_config_file], **kwargs)
            return parser
        finally:
            # Clean up temporary file
            os.unlink(temp_config_file)

    @staticmethod
    def _deep_update(base_dict: Dict[str, Any], update_dict: Dict[str, Any]):
        """Deep update a dictionary with another dictionary."""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                IPsecEvaluatorConfigParser._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
